{"cells": [{"cell_type": "markdown", "id": "73469a6a", "metadata": {}, "source": ["# Chapter 2: Working with Text Data"]}, {"cell_type": "markdown", "id": "0a0f4e1b", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## 2.2 Tokenizing Text"]}, {"cell_type": "code", "execution_count": 13, "id": "5b563bdd", "metadata": {}, "outputs": [], "source": ["import os \n", "import urllib.request\n", "\n", "if not os.path.exists(\"the-verdict.txt\"):\n", "    url = (\"https://raw.githubusercontent.com/rasbt/LLMs-from-scratch/refs/heads/main/ch02/01_main-chapter-code/the-verdict.txt\")\n", "    file_path = \"the-verdict.txt\"\n", "    urllib.request.urlretrieve(url, file_path)\n"]}, {"cell_type": "code", "execution_count": 14, "id": "309a59aa", "metadata": {}, "outputs": [], "source": ["with open(\"the-verdict.txt\", \"r\", encoding=\"utf-8\") as f:\n", "    raw_text = f.read()"]}, {"cell_type": "code", "execution_count": 16, "id": "1d5317fb", "metadata": {}, "outputs": [{"data": {"text/plain": ["20479"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["len(raw_text)"]}, {"cell_type": "code", "execution_count": 17, "id": "9bfd6c3c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Hello,', ' ', 'world.', ' ', 'This,', ' ', 'is', ' ', 'a', ' ', 'test.']\n"]}], "source": ["import re \n", "\n", "text = \"Hello, world. This, is a test.\"\n", "result = re.split(r'(\\s)', text)\n", "\n", "print(result)"]}, {"cell_type": "code", "execution_count": 18, "id": "163f7855-6db8-4f4d-a7f9-281b0679b522", "metadata": {}, "outputs": [], "source": ["result = re.split(r'([,.]|\\s)', text)"]}, {"cell_type": "code", "execution_count": 19, "id": "a9cecef0-4db6-4531-8c98-5b9640ed31f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Hello', ',', '', ' ', 'world', '.', '', ' ', 'This', ',', '', ' ', 'is', ' ', 'a', ' ', 'test', '.', '']\n"]}], "source": ["print(result)"]}, {"cell_type": "code", "execution_count": 20, "id": "6396af9c-45c2-44ab-b5d9-2b5600d50969", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Hello', ',', 'world', '.', 'This', ',', 'is', 'a', 'test', '.']\n"]}], "source": ["result = [item for item in result if item.strip()]\n", "print(result)"]}, {"cell_type": "code", "execution_count": 21, "id": "abeb9423-bd0a-4403-820f-d080c64adf60", "metadata": {}, "outputs": [], "source": ["text = \"Hello, world. Is this-- a test?\"\n", "\n", "result = re.split(r'([,.:;?_!\"()\\']|--|\\s)', raw_text)\n", "result = [item.strip() for item in result if item.strip()]\n", "preprocessed = result"]}, {"cell_type": "code", "execution_count": 22, "id": "f3808917-1057-424c-a617-f19bd22b596a", "metadata": {}, "outputs": [{"data": {"text/plain": ["4690"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["len(result)"]}, {"cell_type": "markdown", "id": "c81f4abc", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["# 2.3 Converting tokens into tokens IDs\n"]}, {"cell_type": "code", "execution_count": 23, "id": "46f5c1f4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1130\n"]}], "source": ["all_worlds = sorted(set(preprocessed))\n", "vocab_size = len(all_worlds)\n", "print(vocab_size)"]}, {"cell_type": "code", "execution_count": 24, "id": "795086fe", "metadata": {}, "outputs": [], "source": ["vocab = {token:integer for integer,token in enumerate(all_worlds)}\n", "# vocab"]}, {"cell_type": "code", "execution_count": 25, "id": "2ca61e92-a3be-44d6-8209-efbd93f82d54", "metadata": {}, "outputs": [], "source": ["class SimpleTokenizerV1:\n", "    def __init__(self, vocab):\n", "        self.str_to_int = vocab\n", "        self.int_to_str = {i:s for s,i in vocab.items()}\n", "\n", "    def encode(self, text):\n", "        preprocessed = re.split(r'([,.:;?_!\"()\\']|--|\\s)', text)\n", "\n", "        preprocessed = [\n", "            item.strip() for item in preprocessed if item.strip()\n", "        ]\n", "        ids = [self.str_to_int[s] for s in preprocessed]\n", "        return ids\n", "\n", "    def decode(self, ids):\n", "        text = \" \".join([self.int_to_str[i] for i in ids])\n", "        text = re.sub(r'\\s+([,.?!\"()\\'])', r'\\1', text)\n", "        return text"]}, {"cell_type": "code", "execution_count": 26, "id": "e81c55f0", "metadata": {}, "outputs": [], "source": ["tokenizer = SimpleTokenizerV1(vocab)"]}, {"cell_type": "code", "execution_count": 27, "id": "2d1f2e34-bc7b-4029-8ca8-48fb7849f088", "metadata": {}, "outputs": [], "source": ["text = \"\"\"\"It's the last he painted, you know,\" Mrs. <PERSON><PERSON><PERSON> said with pardonable pride.\"\"\""]}, {"cell_type": "code", "execution_count": 28, "id": "172deecf-c532-458a-82ce-5157af725366", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 56, 2, 850, 988, 602, 533, 746, 5, 1126, 596, 5, 1, 67, 7, 38, 851, 1108, 754, 793, 7]\n"]}], "source": ["ids = tokenizer.encode(text)\n", "print(ids)"]}, {"cell_type": "code", "execution_count": 29, "id": "5937c21e-96c7-419f-b843-b1640a309aa5", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\" It\\' s the last he painted, you know,\" Mrs. <PERSON><PERSON><PERSON> said with pardonable pride.'"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.decode(ids)"]}, {"cell_type": "code", "execution_count": 30, "id": "5df0bdf1-7a63-4724-a6d4-00d1e86574e9", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\" It\\' s the last he painted, you know,\" Mrs. <PERSON><PERSON><PERSON> said with pardonable pride.'"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.decode(tokenizer.encode(text))"]}, {"cell_type": "markdown", "id": "0651bdaf", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["# 2.4 Adding special context tokens\n"]}, {"cell_type": "code", "execution_count": 31, "id": "a3e5c0b3", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'Hello'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[31], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m text \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON><PERSON>, do you like tea. is this-- a test?\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m----> 3\u001b[0m \u001b[43m<PERSON><PERSON><PERSON>\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencode\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtext\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[25], line 12\u001b[0m, in \u001b[0;36mSimpleTokenizerV1.encode\u001b[0;34m(self, text)\u001b[0m\n\u001b[1;32m      7\u001b[0m preprocessed \u001b[38;5;241m=\u001b[39m re\u001b[38;5;241m.\u001b[39msplit(\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m([,.:;?_!\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m()\u001b[39m\u001b[38;5;130;01m\\'\u001b[39;00m\u001b[38;5;124m]|--|\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124ms)\u001b[39m\u001b[38;5;124m'\u001b[39m, text)\n\u001b[1;32m      9\u001b[0m preprocessed \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m     10\u001b[0m     item\u001b[38;5;241m.\u001b[39mstrip() \u001b[38;5;28;01mfor\u001b[39;00m item \u001b[38;5;129;01min\u001b[39;00m preprocessed \u001b[38;5;28;01mif\u001b[39;00m item\u001b[38;5;241m.\u001b[39mstrip()\n\u001b[1;32m     11\u001b[0m ]\n\u001b[0;32m---> 12\u001b[0m ids \u001b[38;5;241m=\u001b[39m [\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstr_to_int[s] \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m preprocessed]\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m ids\n", "Cell \u001b[0;32mIn[25], line 12\u001b[0m, in \u001b[0;36m<listcomp>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m      7\u001b[0m preprocessed \u001b[38;5;241m=\u001b[39m re\u001b[38;5;241m.\u001b[39msplit(\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m([,.:;?_!\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m()\u001b[39m\u001b[38;5;130;01m\\'\u001b[39;00m\u001b[38;5;124m]|--|\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124ms)\u001b[39m\u001b[38;5;124m'\u001b[39m, text)\n\u001b[1;32m      9\u001b[0m preprocessed \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m     10\u001b[0m     item\u001b[38;5;241m.\u001b[39mstrip() \u001b[38;5;28;01mfor\u001b[39;00m item \u001b[38;5;129;01min\u001b[39;00m preprocessed \u001b[38;5;28;01mif\u001b[39;00m item\u001b[38;5;241m.\u001b[39mstrip()\n\u001b[1;32m     11\u001b[0m ]\n\u001b[0;32m---> 12\u001b[0m ids \u001b[38;5;241m=\u001b[39m [\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstr_to_int\u001b[49m\u001b[43m[\u001b[49m\u001b[43ms\u001b[49m\u001b[43m]\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m preprocessed]\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m ids\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Hello'"]}], "source": ["text = \"Hello, do you like tea. is this-- a test?\"\n", "\n", "tokenizer.encode(text)"]}, {"cell_type": "code", "execution_count": 32, "id": "2c47dafc", "metadata": {}, "outputs": [], "source": ["all_tokens = sorted(list(set(preprocessed)))\n", "all_tokens.extend([\"<|endoftext|>\", \"<|unk|>\"])\n", "\n", "vocab = {token:integer for integer,token in enumerate(all_tokens)}"]}, {"cell_type": "code", "execution_count": 33, "id": "6d3666c8", "metadata": {}, "outputs": [{"data": {"text/plain": ["1132"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["len(vocab.items())"]}, {"cell_type": "code", "execution_count": 35, "id": "49004800-3081-40d2-8aac-8766d3f10cd1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('younger', 1127)\n", "('your', 1128)\n", "('yourself', 1129)\n", "('<|endoftext|>', 1130)\n", "('<|unk|>', 1131)\n"]}], "source": ["for i, item in enumerate(list(vocab.items())[-5:]):\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 36, "id": "091fb06a-fb14-4589-90cd-9759dafa8529", "metadata": {}, "outputs": [], "source": ["class SimpleTokenizerV2:\n", "    def __init__(self, vocab):\n", "        self.str_to_int = vocab\n", "        self.int_to_str = {i:s for s,i in vocab.items()}\n", "\n", "    def encode(self, text):\n", "        preprocessed = re.split(r'([,.:;?_!\"()\\']|--|\\s)', text)\n", "\n", "        preprocessed = [\n", "            item.strip() for item in preprocessed if item.strip()\n", "        ]\n", "        preprocessed = [\n", "            item if item in self.str_to_int\n", "            else \"<|unk|>\" for item in preprocessed\n", "        ]\n", "            \n", "        ids = [self.str_to_int[s] for s in preprocessed]\n", "        return ids\n", "\n", "    def decode(self, ids):\n", "        text = \" \".join([self.int_to_str[i] for i in ids])\n", "        text = re.sub(r'\\s+([,.?!\"()\\'])', r'\\1', text)\n", "        return text"]}, {"cell_type": "code", "execution_count": 40, "id": "6844b036-628c-4f6b-838e-66b5ecd0acf4", "metadata": {}, "outputs": [], "source": ["tokenizer = SimpleTokenizerV2(vocab)"]}, {"cell_type": "code", "execution_count": 41, "id": "d85be461-ac8d-4a3d-9d88-eddb9fa3a706", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1131, 5, 355, 1126, 628, 975, 7, 584, 999, 6, 115, 1131, 10]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.encode(text)"]}, {"cell_type": "code", "execution_count": 42, "id": "55992cc3-2775-4cef-a55d-470f5bd7d593", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<|unk|>, do you like tea. is this -- a <|unk|>?'"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.decode(tokenizer.encode(text))"]}, {"cell_type": "markdown", "id": "36113394", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["# 2.5 Byte Pair Encoding"]}, {"cell_type": "code", "execution_count": 51, "id": "6a2055a3-eeee-4f9b-9177-cd12852602cc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['gpt2', 'r50k_base', 'p50k_base', 'p50k_edit', 'cl100k_base', 'o200k_base']\n"]}], "source": ["import tiktoken\n", "tiktoken.__version__\n", "print(tiktoken.list_encoding_names())"]}, {"cell_type": "code", "execution_count": null, "id": "c44841dc-a646-4589-b8e8-655156742e36", "metadata": {}, "outputs": [], "source": ["tokenizer = tiktoken.get_encoding(\"o200k_base\")"]}, {"cell_type": "code", "execution_count": 142, "id": "8d8026cf-8ae5-4860-89ab-6062c012ffb2", "metadata": {}, "outputs": [{"data": {"text/plain": ["[15496, 995]"]}, "execution_count": 142, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.encode(\"Hello world\")"]}, {"cell_type": "code", "execution_count": 143, "id": "30198609-91f2-4a4b-a24f-d241678c2cfc", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello world'"]}, "execution_count": 143, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.decode(tokenizer.encode(\"Hello world\"))"]}, {"cell_type": "code", "execution_count": 144, "id": "f26f955d-348a-40dc-9eea-885a070cf47a", "metadata": {}, "outputs": [{"data": {"text/plain": ["[15496,\n", " 11,\n", " 466,\n", " 345,\n", " 588,\n", " 8887,\n", " 30,\n", " 220,\n", " 50256,\n", " 554,\n", " 262,\n", " 4252,\n", " 18250,\n", " 8812,\n", " 2114,\n", " 1659,\n", " 617,\n", " 34680,\n", " 27271,\n", " 13]"]}, "execution_count": 144, "metadata": {}, "output_type": "execute_result"}], "source": ["text = (\n", "    \"Hello, do you like tea? <|endoftext|> In the sunlit terraces\"\n", "    \"of someunknownPlace.\"\n", ")\n", "tokenizer.encode(text, allowed_special={\"<|endoftext|>\"})"]}, {"cell_type": "markdown", "id": "86b567ee", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["# 2.6 <PERSON> Sampling with a sliding window"]}, {"cell_type": "code", "execution_count": 145, "id": "8a481dc4-a849-4dc9-a1e0-11c368864573", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5145\n"]}], "source": ["with open(\"the-verdict.txt\", \"r\", encoding=\"utf-8\") as f:\n", "    raw_text = f.read()\n", "\n", "enc_text = tokenizer.encode(raw_text)\n", "print(len(enc_text))"]}, {"cell_type": "code", "execution_count": 146, "id": "1d1ce80f-0d09-44ca-97df-3a252de508ee", "metadata": {}, "outputs": [], "source": ["enc_sample = enc_text[50:]"]}, {"cell_type": "code", "execution_count": 147, "id": "d75c55ed-632e-4701-a837-e10230554d20", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["x: [290, 4920, 2241, 287, 257, 4489, 64, 319, 262, 34686]\n", "y:       [4920, 2241, 287, 257, 4489, 64, 319, 262, 34686, 41976]\n"]}], "source": ["context_size = 10\n", "\n", "x = enc_sample[:context_size]\n", "y = enc_sample[1:context_size+1]\n", "\n", "print(f\"x: {x}\")\n", "print(f\"y:       {y}\")"]}, {"cell_type": "code", "execution_count": 148, "id": "93f22a22-efc3-4f23-a1bb-1883381bcc78", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" and ---->  established\n", " and established ---->  himself\n", " and established himself ---->  in\n", " and established himself in ---->  a\n", " and established himself in a ---->  vill\n", " and established himself in a vill ----> a\n", " and established himself in a villa ---->  on\n", " and established himself in a villa on ---->  the\n", " and established himself in a villa on the ---->  <PERSON><PERSON>\n", " and established himself in a villa on the Riv ----> iera\n"]}], "source": ["for i in range(1, context_size+1):\n", "    context = enc_sample[:i]\n", "    desired = enc_sample[i]\n", "\n", "    print(tokenizer.decode(context), \"---->\", tokenizer.decode([desired]))"]}, {"cell_type": "code", "execution_count": 149, "id": "4fb1526f-8c96-4d70-ad27-719c8373e9c4", "metadata": {}, "outputs": [{"data": {"text/plain": ["'2.7.1'"]}, "execution_count": 149, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch \n", "torch.__version__"]}, {"cell_type": "code", "execution_count": 150, "id": "c0c4a0c5-ef18-48c9-bd50-e856afc4d6b6", "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch.utils.data import Dataset, DataLoader\n", "\n", "class GPTDatasetV1(Dataset):\n", "    def __init__(self, txt, tokenizer, max_length, stride):\n", "        self.input_ids = []\n", "        self.target_ids = []\n", "\n", "        token_ids = tokenizer.encode(txt, allowed_special={\"<|endoftext|>\"})\n", "\n", "        for i in range(0, len(token_ids) - max_length, stride):\n", "            input_chunk = token_ids[i:i + max_length]\n", "            target_chunk = token_ids[i + 1:i + max_length + 1]\n", "            self.input_ids.append(torch.tensor(input_chunk, dtype=torch.long))\n", "            self.target_ids.append(torch.tensor(target_chunk, dtype=torch.long))\n", "\n", "    def __len__(self):\n", "        return len(self.input_ids)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.input_ids[idx], self.target_ids[idx]\n"]}, {"cell_type": "code", "execution_count": null, "id": "141ddc28-6b56-4d48-b28b-c2de69d17417", "metadata": {}, "outputs": [], "source": ["def create_dataloader_v1(txt, batch_size=10, max_length=256, stride=128, shuffle=True, drop_last=True, num_workers=0):\n", "\n", "    tokenizer = tiktoken.get_encoding(\"o200k_base\")\n", "\n", "    dataset = GPTDatasetV1(txt, tokenizer, max_length, stride)\n", "\n", "    dataloader = DataLoader(\n", "        dataset,\n", "        batch_size=batch_size,\n", "        shuffle=shuffle,\n", "        drop_last=drop_last,\n", "        num_workers=num_workers\n", "    ) \n", "\n", "    return dataloader"]}, {"cell_type": "code", "execution_count": 152, "id": "27114fa4-098e-4035-9e28-0b11890095a0", "metadata": {}, "outputs": [], "source": ["with open(\"the-verdict.txt\", \"r\", encoding=\"utf-8\") as f:\n", "    raw_text = f.read()"]}, {"cell_type": "code", "execution_count": 153, "id": "58bafda6-8745-4abe-a327-c41c90dd3347", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input:\n", " tensor([[   40,   367,  2885,  1464],\n", "        [ 1807,  3619,   402,   271],\n", "        [10899,  2138,   257,  7026],\n", "        [15632,   438,  2016,   257],\n", "        [  922,  5891,  1576,   438],\n", "        [  568,   340,   373,   645],\n", "        [ 1049,  5975,   284,   502],\n", "        [  284,  3285,   326,    11]])\n", "\n", "Targets:\n", " tensor([[  367,  2885,  1464,  1807],\n", "        [ 3619,   402,   271, 10899],\n", "        [ 2138,   257,  7026, 15632],\n", "        [  438,  2016,   257,   922],\n", "        [ 5891,  1576,   438,   568],\n", "        [  340,   373,   645,  1049],\n", "        [ 5975,   284,   502,   284],\n", "        [ 3285,   326,    11,   287]])\n"]}], "source": ["dataloader = create_dataloader_v1(\n", "    raw_text, batch_size=8, max_length=4, stride=4, shuffle=False\n", ")\n", "\n", "data_iter = iter(dataloader)\n", "inputs, targets = next(data_iter)\n", "print(\"Input:\\n\", inputs)\n", "print(\"\\nTargets:\\n\", targets)"]}, {"cell_type": "markdown", "id": "bd7a73f8", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["# 2.7 Creating token embeddings"]}, {"cell_type": "code", "execution_count": 154, "id": "012154da-5e8a-41be-a6fc-87aacf460f2d", "metadata": {}, "outputs": [], "source": ["input_ids = torch.tensor([ 2, 3, 5, 1])"]}, {"cell_type": "code", "execution_count": 155, "id": "345168cf-8adb-4fc9-b9fc-4439ac99c16a", "metadata": {}, "outputs": [], "source": ["vocab_size = 6 \n", "output_dim = 3\n", "\n", "#torch.manual_seed(123)\n", "embedding_layer = torch.nn.Embedding(tokenizer.n_vocab, output_dim)"]}, {"cell_type": "code", "execution_count": 156, "id": "1f0cfed0-e150-46ea-810c-1987c5e40a50", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameter containing:\n", "tensor([[ 0.5130, -2.0963, -1.4732],\n", "        [-0.1167,  1.8253, -0.2560],\n", "        [-1.8347, -0.8268,  2.0210],\n", "        ...,\n", "        [ 2.6816,  0.6572, -0.0398],\n", "        [-0.0943, -1.0065, -0.3656],\n", "        [-0.0912, -1.2401,  0.3224]], requires_grad=True)\n"]}], "source": ["print(embedding_layer.weight)"]}, {"cell_type": "code", "execution_count": 157, "id": "d774071b-da16-45ac-80d5-7622120f908b", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.5714,  0.1599, -0.6303]], grad_fn=<EmbeddingBackward0>)"]}, "execution_count": 157, "metadata": {}, "output_type": "execute_result"}], "source": ["embedding_layer(torch.tensor([3]))"]}, {"cell_type": "code", "execution_count": 158, "id": "a9376ee5-4a98-4d79-bf90-7aaabc994ad1", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[-1.8347, -0.8268,  2.0210],\n", "        [ 0.5714,  0.1599, -0.6303],\n", "        [ 0.2972, -0.4593, -1.1858],\n", "        [-0.1167,  1.8253, -0.2560]], grad_fn=<EmbeddingBackward0>)"]}, "execution_count": 158, "metadata": {}, "output_type": "execute_result"}], "source": ["embedding_layer(input_ids)"]}, {"cell_type": "code", "execution_count": 159, "id": "725b188f-8077-4203-a597-4d539ec8359b", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([2, 3, 5, 1])"]}, "execution_count": 159, "metadata": {}, "output_type": "execute_result"}], "source": ["input_ids"]}, {"cell_type": "markdown", "id": "e5a74284-b7e3-4c99-aec3-c0c8f5fa52b1", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["# 2.8 Encoding word position"]}, {"cell_type": "code", "execution_count": 160, "id": "a50037d0-89cd-4bbe-8999-8c7b54221a06", "metadata": {}, "outputs": [], "source": ["vocab_size = 50257\n", "output_dim = 256\n", "\n", "token_embedding_layer = torch.nn.Embedding(vocab_size, output_dim)"]}, {"cell_type": "code", "execution_count": 161, "id": "89937667-e650-4618-bcbb-984aae898413", "metadata": {}, "outputs": [], "source": ["max_length = 4 \n", "dataloader = create_dataloader_v1(\n", "    raw_text, batch_size=8, max_length=max_length,\n", "    stride=max_length, shuffle=False\n", ")\n", "data_iter = iter(dataloader)\n", "inputs, targets = next(data_iter)"]}, {"cell_type": "code", "execution_count": 162, "id": "1c9d8b6b-3ab6-44c0-9502-bacc2f548f27", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([8, 4, 256])"]}, "execution_count": 162, "metadata": {}, "output_type": "execute_result"}], "source": ["token_embeddings = token_embedding_layer(inputs)\n", "token_embeddings.shape"]}, {"cell_type": "code", "execution_count": 163, "id": "7d697d58-707c-4b95-b943-5911e3504110", "metadata": {}, "outputs": [], "source": ["context_length = max_length\n", "pos_embedding_layer = torch.nn.Embedding(context_length, output_dim)"]}, {"cell_type": "code", "execution_count": 164, "id": "dfc30916-6255-498e-9702-f08e8acda3c4", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0, 1, 2, 3])"]}, "execution_count": 164, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.arange(max_length)"]}, {"cell_type": "code", "execution_count": null, "id": "4f3f24aa-fbf2-4ff6-a221-d302a7766dee", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "100d420f-d2e7-4082-89e3-9150cb4fd647", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 165, "id": "be14a736-cfd4-4ce5-a029-e4673e5f1ea8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([4, 256])\n"]}], "source": ["pos_embeddings = pos_embedding_layer(torch.arange(max_length))\n", "print(pos_embeddings.shape)"]}, {"cell_type": "code", "execution_count": 166, "id": "7c7b693b-a467-4f1a-8285-10481bf46d59", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([8, 4, 256])\n"]}], "source": ["input_embeddings = token_embeddings + pos_embeddings\n", "print(input_embeddings.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "6778378d-3670-41ef-9ff1-dc2ec58a6f07", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}