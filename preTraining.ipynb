{"cells": [{"cell_type": "markdown", "id": "038a6466", "metadata": {}, "source": ["# PRETRAINING ON UNLABELED DATA"]}, {"cell_type": "markdown", "id": "41da8a69", "metadata": {}, "source": ["## 5.1 Evaluating generated text models"]}, {"cell_type": "markdown", "id": "7422fcb6", "metadata": {}, "source": ["### 5.1.1 Using GPT to generate text"]}, {"cell_type": "code", "execution_count": 202, "id": "e132998c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch==2.7.1\n", "tiktoken==0.9.0\n", "matplotlib==3.10.3\n", "tensorflow==2.19.0\n", "numpy==2.0.2\n"]}], "source": ["from importlib.metadata import version\n", "\n", "pkgs = [\n", "    \"torch\",\n", "    \"tiktoken\",\n", "    \"matplotlib\",\n", "    \"tensorflow\",\n", "    \"numpy\"\n", "]\n", "\n", "for p in pkgs:\n", "    print(f\"{p}=={version(p)}\")"]}, {"cell_type": "code", "execution_count": 203, "id": "924a8001", "metadata": {}, "outputs": [{"data": {"text/plain": ["<module 'previous_chapters' from '/Users/<USER>/Documents/AIbYME/previous_chapters.py'>"]}, "execution_count": 203, "metadata": {}, "output_type": "execute_result"}], "source": ["import importlib\n", "import previous_chapters\n", "importlib.reload(previous_chapters)\n"]}, {"cell_type": "code", "execution_count": 278, "id": "cab0349b", "metadata": {}, "outputs": [], "source": ["from previous_chapters import GPTModel\n", "\n", "GPT_CONFIG_124M = {\n", "    \"vocab_size\": 50257,\n", "    \"context_length\": 1024,\n", "    \"emb_dim\": 1024,\n", "    \"n_heads\": 16,\n", "    \"n_layers\": 24,\n", "    \"drop_rate\": 0.1,\n", "    \"qkv_bias\": <PERSON><PERSON><PERSON>,\n", "}"]}, {"cell_type": "code", "execution_count": 205, "id": "ea1689c0", "metadata": {}, "outputs": [], "source": ["import torch\n", "\n", "torch.manual_seed(123)\n", "model = GPTModel(GPT_CONFIG_124M)\n", "model.eval(); \n"]}, {"cell_type": "code", "execution_count": 206, "id": "51e3bda2", "metadata": {}, "outputs": [], "source": ["import tiktoken\n", "from previous_chapters import generate_text_simple\n", "\n", "def text_to_token_ids(text, tokenizer):\n", "    encoded = tokenizer.encode(text, allowed_special={\"<|endoftext|>\"})\n", "    encoded_tensor = torch.tensor(encoded).unsqueeze(0)\n", "    return encoded_tensor"]}, {"cell_type": "code", "execution_count": 207, "id": "12de3dd0", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[6109, 3626, 6100,  345]])"]}, "execution_count": 207, "metadata": {}, "output_type": "execute_result"}], "source": ["start_context = \"Every effort moves you\"\n", "tokenizer = tiktoken.get_encoding(\"gpt2\")\n", "\n", "token_ids = text_to_token_ids(start_context, tokenizer)\n", "token_ids"]}, {"cell_type": "code", "execution_count": 208, "id": "92f05699", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Every effort moves you'"]}, "execution_count": 208, "metadata": {}, "output_type": "execute_result"}], "source": ["def token_ids_to_text(token_ids, tokenizer):\n", "    flat = token_ids.squeeze(0)\n", "    return tokenizer.decode(flat.tolist())\n", "\n", "token_ids_to_text(token_ids, tokenizer)"]}, {"cell_type": "code", "execution_count": 209, "id": "d80847f0", "metadata": {}, "outputs": [], "source": ["token_ids = generate_text_simple(\n", "    model = model,\n", "    idx = text_to_token_ids(start_context, tokenizer),\n", "    max_new_tokens = 10,\n", "    context_size = GPT_CONFIG_124M[\"context_length\"]\n", ")"]}, {"cell_type": "code", "execution_count": 210, "id": "d3570158", "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([14])"]}, "execution_count": 210, "metadata": {}, "output_type": "execute_result"}], "source": ["token_ids.squeeze(0).shape"]}, {"cell_type": "code", "execution_count": 211, "id": "ca2b5b1d", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Every effort moves youDown Pale onslaught}} Occasionally Intermediatening ShelleySkillcin'"]}, "execution_count": 211, "metadata": {}, "output_type": "execute_result"}], "source": ["token_ids_to_text(token_ids, tokenizer)"]}, {"cell_type": "markdown", "id": "7b9acbb1", "metadata": {}, "source": ["### 5.1.2 Calculating the text generation loss: cross-entropy and perplexity"]}, {"cell_type": "code", "execution_count": 212, "id": "af2bf24b", "metadata": {}, "outputs": [], "source": ["inputs = torch.tensor([[16833, 3626, 6100],   # [\"every effort moves\",\n", "                       [40,    1107, 588]])   #  \"I really like\"]\n", "\n", "targets = torch.tensor([[3626, 6100, 345  ],  # [\" effort moves you\",\n", "                        [1107,  588, 11311]]) #  \" really like chocolate\"]"]}, {"cell_type": "code", "execution_count": 213, "id": "b84040b7", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.Si<PERSON>([2, 3, 50257])"]}, "execution_count": 213, "metadata": {}, "output_type": "execute_result"}], "source": ["with torch.no_grad():\n", "    logits = model(inputs)\n", "\n", "logits.shape"]}, {"cell_type": "code", "execution_count": 214, "id": "75a6e408", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.Si<PERSON>([2, 3, 50257])"]}, "execution_count": 214, "metadata": {}, "output_type": "execute_result"}], "source": ["probas = torch.softmax(logits, dim=-1)\n", "probas.shape"]}, {"cell_type": "code", "execution_count": 215, "id": "40575187", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[1.8674e-05, 2.5768e-05, 2.2757e-05,  ..., 9.3835e-06,\n", "          6.6751e-05, 2.9685e-05],\n", "         [1.8274e-05, 1.8299e-05, 1.5381e-05,  ..., 9.2183e-06,\n", "          2.4449e-05, 1.8218e-05],\n", "         [8.8252e-06, 2.1022e-05, 1.8220e-05,  ..., 5.5068e-06,\n", "          2.0420e-05, 3.6044e-05]],\n", "\n", "        [[2.2466e-05, 2.0369e-05, 1.4385e-05,  ..., 7.4545e-06,\n", "          2.9067e-05, 3.5362e-05],\n", "         [2.2894e-05, 1.8715e-05, 1.1672e-05,  ..., 1.7587e-05,\n", "          3.9837e-05, 4.0580e-05],\n", "         [8.6302e-06, 1.6022e-05, 2.1752e-05,  ..., 1.5785e-05,\n", "          4.1843e-05, 3.8676e-05]]])"]}, "execution_count": 215, "metadata": {}, "output_type": "execute_result"}], "source": ["probas"]}, {"cell_type": "code", "execution_count": 216, "id": "97ae62cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Token IDS: tensor([[[25738],\n", "         [49876],\n", "         [14002]],\n", "\n", "        [[37220],\n", "         [49576],\n", "         [45815]]])\n"]}], "source": ["token_ids = torch.argmax(probas, dim=-1, keepdim=True)\n", "print(\"Token IDS:\", token_ids)"]}, {"cell_type": "code", "execution_count": 217, "id": "d91f7f67", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Target batch 1:  effort moves you\n", "Target batch 2:  rationale fractionssels\n"]}], "source": ["print(f\"Target batch 1: {token_ids_to_text(targets[0], tokenizer)}\")\n", "print(f\"Target batch 2: {token_ids_to_text(token_ids[0].flatten(), tokenizer)}\")"]}, {"cell_type": "code", "execution_count": 218, "id": "975bf6fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Text 1:  tensor([6.1811e-06, 1.4285e-05, 1.3743e-05])\n", "Text 2:  tensor([1.8135e-05, 1.2482e-05, 1.5152e-05])\n"]}], "source": ["text_ids = 0\n", "target_probas_1 = probas[text_ids, [0, 1, 2], targets[text_ids]]\n", "print(\"Text 1: \", target_probas_1)\n", "\n", "text_ids = 1\n", "target_probas_2 = probas[text_ids, [0, 1, 2], targets[text_ids]]\n", "print(\"Text 2: \", target_probas_2)"]}, {"cell_type": "code", "execution_count": 219, "id": "e6541da6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([-11.9940, -11.1563, -11.1950, -10.9177, -11.2912, -11.0974])\n"]}], "source": ["log_probas = torch.log(torch.cat((target_probas_1, target_probas_2), dim=0))\n", "print(log_probas)"]}, {"cell_type": "code", "execution_count": 220, "id": "df746cd9", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor(-11.2753)"]}, "execution_count": 220, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.mean(log_probas)\n", "# prints tensor(-10.8765) if thats near 0 then you did it!"]}, {"cell_type": "code", "execution_count": 221, "id": "6cb7767b", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([6, 50257])"]}, "execution_count": 221, "metadata": {}, "output_type": "execute_result"}], "source": ["logits_flat = logits.flatten(0, 1)\n", "logits_flat.shape"]}, {"cell_type": "code", "execution_count": 222, "id": "e6476822", "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([6])"]}, "execution_count": 222, "metadata": {}, "output_type": "execute_result"}], "source": ["targets_flat = targets.flatten()\n", "targets_flat.shape"]}, {"cell_type": "code", "execution_count": 223, "id": "d1cdb960", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor(11.2753)"]}, "execution_count": 223, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.nn.functional.cross_entropy(logits_flat, targets_flat)"]}, {"cell_type": "markdown", "id": "f0a7837a", "metadata": {}, "source": ["### 5.1.3 Calculating the training and validation set losses"]}, {"cell_type": "code", "execution_count": 224, "id": "a0b50330", "metadata": {}, "outputs": [], "source": ["import os\n", "import urllib.request\n", "\n", "file_path = \"the-verdict.txt\"\n", "url = \"https://raw.githubusercontent.com/rasbt/LLMs-from-scratch/main/ch02/01_main-chapter-code/the-verdict.txt\"\n", "\n", "if not os.path.exists(file_path):\n", "    with urllib.request.urlopen(url) as response:\n", "        text_data = response.read().decode('utf-8')\n", "    with open(file_path, \"w\", encoding=\"utf-8\") as file:\n", "        file.write(text_data)\n", "else:\n", "    with open(file_path, \"r\", encoding=\"utf-8\") as file:\n", "        text_data = file.read()"]}, {"cell_type": "code", "execution_count": 225, "id": "978dd601", "metadata": {}, "outputs": [{"data": {"text/plain": ["'I <PERSON><PERSON> always thought <PERSON> rather a cheap genius--though a good fellow enough--so it was no '"]}, "execution_count": 225, "metadata": {}, "output_type": "execute_result"}], "source": ["text_data[:99]"]}, {"cell_type": "code", "execution_count": 226, "id": "14b2c811", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Characters: 20479\n", "Tokens: 5145\n"]}], "source": ["total_characters = len(text_data)\n", "total_tokens = len(tokenizer.encode(text_data))\n", "\n", "print(\"Characters:\", total_characters)\n", "print(\"Tokens:\", total_tokens)"]}, {"cell_type": "code", "execution_count": 227, "id": "3efc02e4", "metadata": {}, "outputs": [], "source": ["from previous_chapters import create_dataloader_v1\n", "train_ratio = 0.90\n", "split_idx = int(train_ratio * len(text_data))\n", "train_data = text_data[:split_idx]\n", "val_data = text_data[split_idx:]\n"]}, {"cell_type": "code", "execution_count": 228, "id": "456ec9ce", "metadata": {}, "outputs": [], "source": ["\n", "torch.manual_seed(123)\n", "train_loader = create_dataloader_v1(\n", "    train_data,\n", "    batch_size=2,\n", "    max_length=GPT_CONFIG_124M[\"context_length\"],\n", "    stride=GPT_CONFIG_124M[\"context_length\"],\n", "    drop_last=True,\n", "    shuffle=True,\n", "    num_workers=0\n", ")\n", "\n", "val_loader = create_dataloader_v1(\n", "    val_data,\n", "    batch_size=2,\n", "    max_length=GPT_CONFIG_124M[\"context_length\"],\n", "    stride=GPT_CONFIG_124M[\"context_length\"],\n", "    drop_last=False,\n", "    shuffle=False,\n", "    num_workers=0\n", ")"]}, {"cell_type": "code", "execution_count": 229, "id": "9bc55f24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train loader:\n", "torch.<PERSON><PERSON>([2, 256]) torch.<PERSON><PERSON>([2, 256])\n", "torch.<PERSON><PERSON>([2, 256]) torch.<PERSON><PERSON>([2, 256])\n", "torch.<PERSON><PERSON>([2, 256]) torch.<PERSON><PERSON>([2, 256])\n", "torch.<PERSON><PERSON>([2, 256]) torch.<PERSON><PERSON>([2, 256])\n", "torch.<PERSON><PERSON>([2, 256]) torch.<PERSON><PERSON>([2, 256])\n", "torch.<PERSON><PERSON>([2, 256]) torch.<PERSON><PERSON>([2, 256])\n", "torch.<PERSON><PERSON>([2, 256]) torch.<PERSON><PERSON>([2, 256])\n", "torch.<PERSON><PERSON>([2, 256]) torch.<PERSON><PERSON>([2, 256])\n", "torch.<PERSON><PERSON>([2, 256]) torch.<PERSON><PERSON>([2, 256])\n", "\n", "Validation loader:\n", "torch.<PERSON><PERSON>([2, 256]) torch.<PERSON><PERSON>([2, 256])\n"]}], "source": ["print(\"Train loader:\")\n", "for x, y in train_loader:\n", "    print(x.shape, y.shape)\n", "\n", "print(\"\\nValidation loader:\")\n", "for x, y in val_loader:\n", "    print(x.shape, y.shape)"]}, {"cell_type": "code", "execution_count": 230, "id": "03cbd5af", "metadata": {}, "outputs": [{"data": {"text/plain": ["512"]}, "execution_count": 230, "metadata": {}, "output_type": "execute_result"}], "source": ["x.numel()"]}, {"cell_type": "code", "execution_count": 231, "id": "897a3199", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training tokens: 4608\n", "Validation tokens: 512\n", "All tokens: 5120\n"]}], "source": ["train_tokens = 0\n", "for input_batch, target_batch in train_loader:\n", "    train_tokens += input_batch.numel()\n", "\n", "val_tokens = 0\n", "for input_batch, target_batch in val_loader:\n", "    val_tokens += input_batch.numel()\n", "\n", "print(\"Training tokens:\", train_tokens)\n", "print(\"Validation tokens:\", val_tokens)\n", "print(\"All tokens:\", train_tokens + val_tokens)"]}, {"cell_type": "code", "execution_count": 232, "id": "f2daa394", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 232, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.cuda.is_available()"]}, {"cell_type": "code", "execution_count": 233, "id": "687192b1", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 233, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.mps.is_available()"]}, {"cell_type": "code", "execution_count": 234, "id": "2ba91a03", "metadata": {}, "outputs": [{"data": {"text/plain": ["device(type='cpu')"]}, "execution_count": 234, "metadata": {}, "output_type": "execute_result"}], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "device"]}, {"cell_type": "code", "execution_count": 235, "id": "0f7fc3f3", "metadata": {}, "outputs": [], "source": ["model.to(device); "]}, {"cell_type": "code", "execution_count": 236, "id": "b7253918", "metadata": {}, "outputs": [], "source": ["def calc_loss_batch(input_batch, target_batch, model, device):\n", "    input_batch, target_batch = input_batch.to(device), target_batch.to(device)\n", "    logits = model(input_batch)\n", "    loss = torch.nn.functional.cross_entropy(logits.flatten(0, 1), target_batch.flatten())\n", "    return loss\n", "\n", "\n", "def calc_loss_loader(data_loader, model, device, num_batches=None):\n", "    total_loss = 0.\n", "    if len(data_loader) == 0:\n", "        return float(\"nan\")\n", "    elif num_batches is None:\n", "        num_batches = len(data_loader)\n", "    else:\n", "        \n", "        num_batches = min(num_batches, len(data_loader))\n", "    for i, (input_batch, target_batch) in enumerate(data_loader):\n", "        if i < num_batches:\n", "            loss = calc_loss_batch(input_batch, target_batch, model, device)\n", "            total_loss += loss.item()\n", "        else:\n", "            break\n", "    return total_loss / num_batches"]}, {"cell_type": "code", "execution_count": 237, "id": "e461b2b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train loss: 11.0089\n", "Validation loss: 10.9586\n"]}], "source": ["torch.manual_seed(123)\n", "\n", "with torch.no_grad():\n", "    train_loss = calc_loss_loader(train_loader, model, device)\n", "    val_loss = calc_loss_loader(val_loader, model, device)\n", "\n", "print(f\"Train loss: {train_loss:.4f}\")\n", "print(f\"Validation loss: {val_loss:.4f}\")"]}, {"cell_type": "code", "execution_count": 238, "id": "91c5c925", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor(57448.8125)"]}, "execution_count": 238, "metadata": {}, "output_type": "execute_result"}], "source": ["# perplexity = exp(loss)\n", "torch.exp(torch.tensor(val_loss))"]}, {"cell_type": "markdown", "id": "435ccbf1", "metadata": {}, "source": ["## 5.2 TRAINING LLM "]}, {"cell_type": "code", "execution_count": 239, "id": "1ea31094", "metadata": {}, "outputs": [], "source": ["def train_model_simple(model, train_loader, val_loader, optimizer, device, num_epochs,\n", "                       eval_freq, eval_iter, start_context, tokenizer):\n", "    train_losses, val_losses, track_tokens_seen = [], [], []\n", "    tokens_seen, global_step = 0, -1\n", "\n", "    for epoch in range(num_epochs):\n", "        model.train() \n", "        \n", "        for input_batch, target_batch in train_loader:\n", "            optimizer.zero_grad() \n", "            loss = calc_loss_batch(input_batch, target_batch, model, device)\n", "            loss.backward()\n", "            optimizer.step()\n", "            tokens_seen += input_batch.numel()\n", "            global_step += 1\n", "\n", "            if global_step % eval_freq == 0:\n", "                train_loss, val_loss = evaluate_model(\n", "                    model, train_loader, val_loader, device, eval_iter)\n", "                train_losses.append(train_loss)\n", "                val_losses.append(val_loss)\n", "                track_tokens_seen.append(tokens_seen)\n", "                print(f\"Ep {epoch+1} (Step {global_step:06d}): \"\n", "                      f\"Train loss {train_loss:.3f}, Val loss {val_loss:.3f}\")\n", "\n", "        generate_and_print_sample(\n", "            model, tokenizer, device, start_context\n", "        )\n", "\n", "    return train_losses, val_losses, track_tokens_seen\n", "\n", "\n", "def evaluate_model(model, train_loader, val_loader, device, eval_iter):\n", "    model.eval()\n", "    with torch.no_grad():\n", "        train_loss = calc_loss_loader(train_loader, model, device, num_batches=eval_iter)\n", "        val_loss = calc_loss_loader(val_loader, model, device, num_batches=eval_iter)\n", "    model.train()\n", "    return train_loss, val_loss\n", "\n", "\n", "def generate_and_print_sample(model, tokenizer, device, start_context):\n", "    model.eval()\n", "    context_size = model.pos_emb.weight.shape[0]\n", "    encoded = text_to_token_ids(start_context, tokenizer).to(device)\n", "    with torch.no_grad():\n", "        token_ids = generate_text_simple(\n", "            model=model, idx=encoded,\n", "            max_new_tokens=50, context_size=context_size\n", "        )\n", "    decoded_text = token_ids_to_text(token_ids, tokenizer)\n", "    print(decoded_text.replace(\"\\n\", \" \"))  \n", "    model.train()"]}, {"cell_type": "code", "execution_count": 240, "id": "871bd141", "metadata": {}, "outputs": [], "source": ["torch.manual_seed(123)\n", "model = GPTModel(GPT_CONFIG_124M)\n", "model.to(device)\n", "optimizer = torch.optim.AdamW(model.parameters(), lr=0.0004, weight_decay=0.1)"]}, {"cell_type": "code", "execution_count": 242, "id": "0ab46bf5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ep 1 (Step 000000): Train loss 6.047, Val loss 6.659\n", "Ep 1 (Step 000005): Train loss 6.004, Val loss 6.689\n", "Every effort moves you...,... G. G. G. G. G. \". \".     \".  \". \".     \". \".   \n"]}], "source": ["num_epochs = 1\n", "train_losses, val_losses, tokens_seen = train_model_simple(\n", "    model, train_loader, val_loader, optimizer, device, num_epochs,\n", "    eval_freq=5, eval_iter=5, start_context=\"Every effort moves you\",\n", "    tokenizer=tokenizer\n", ")"]}, {"cell_type": "code", "execution_count": 243, "id": "27bade5b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 500x300 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "from matplotlib.ticker import MaxNLocator\n", "\n", "\n", "def plot_losses(epochs_seen, tokens_seen, train_losses, val_losses):\n", "    fig, ax1 = plt.subplots(figsize=(5, 3))\n", "\n", "    ax1.plot(epochs_seen, train_losses, label=\"Training loss\")\n", "    ax1.plot(epochs_seen, val_losses, linestyle=\"-.\", label=\"Validation loss\")\n", "    ax1.set_xlabel(\"Epochs\")\n", "    ax1.set_ylabel(\"Loss\")\n", "    ax1.legend(loc=\"upper right\")\n", "    ax1.xaxis.set_major_locator(MaxNLocator(integer=True))\n", "\n", "    ax2 = ax1.twiny() \n", "    ax2.plot(tokens_seen, train_losses, alpha=0) \n", "    ax2.set_xlabel(\"Tokens seen\")\n", "\n", "    fig.tight_layout() \n", "    plt.savefig(\"loss-plot.pdf\")\n", "    plt.show()\n", "\n", "epochs_tensor = torch.linspace(0, num_epochs, len(train_losses))\n", "plot_losses(epochs_tensor, tokens_seen, train_losses, val_losses)"]}, {"cell_type": "markdown", "id": "db18d11b", "metadata": {}, "source": ["### 5.3 Decoding strategies to control randomness"]}, {"cell_type": "code", "execution_count": null, "id": "b4e5b66a", "metadata": {}, "outputs": [], "source": ["import token\n", "\n", "\n", "model.to(\"cpu\")\n", "model.eval()\n", "\n", "tokenizer = tiktoken.get_encoding(\"o200k_base\")\n", "\n", "token_ids = generate_text_simple(\n", "    model=model,\n", "    idx = text_to_token_ids(\"Every effort moves you\", tokenizer),\n", "    max_new_tokens=25,\n", "    context_size=GPT_CONFIG_124M[\"context_length\"]\n", ")\n", "\n", "# print(\"Output text:\\n\", token_ids_to_text(token_ids, tokenizer))"]}, {"cell_type": "markdown", "id": "5babe44d", "metadata": {}, "source": ["### 5.3.1 Temperature scaling"]}, {"cell_type": "code", "execution_count": 244, "id": "1a61f39d", "metadata": {}, "outputs": [{"data": {"text/plain": ["{0: 'closer',\n", " 1: 'every',\n", " 2: 'effort',\n", " 3: 'forward',\n", " 4: 'inches',\n", " 5: 'moves',\n", " 6: 'pizza',\n", " 7: 'toward',\n", " 8: 'you'}"]}, "execution_count": 244, "metadata": {}, "output_type": "execute_result"}], "source": ["vocab = { \n", "    \"closer\": 0,\n", "    \"every\": 1, \n", "    \"effort\": 2, \n", "    \"forward\": 3,\n", "    \"inches\": 4,\n", "    \"moves\": 5, \n", "    \"pizza\": 6,\n", "    \"toward\": 7,\n", "    \"you\": 8,\n", "}\n", "inverse_vocab = {v: k for k, v in vocab.items()}\n", "inverse_vocab\n"]}, {"cell_type": "code", "execution_count": 245, "id": "9459a462", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([6.0907e-02, 1.6313e-03, 1.0019e-04, 5.7212e-01, 3.4190e-03, 1.3257e-04,\n", "        1.0120e-04, 3.5758e-01, 4.0122e-03])"]}, "execution_count": 245, "metadata": {}, "output_type": "execute_result"}], "source": ["# simple the model(x) needs to do this\n", "next_token_logits = torch.tensor(\n", "    [4.51, 0.89, -1.90, 6.75, 1.63, -1.62, -1.89, 6.28, 1.79]\n", ")\n", "probas = torch.softmax(next_token_logits, dim=-1)\n", "probas"]}, {"cell_type": "code", "execution_count": 246, "id": "5950ecbd", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 246, "metadata": {}, "output_type": "execute_result"}], "source": ["next_token_id = torch.argmax(probas).item()\n", "next_token_id"]}, {"cell_type": "code", "execution_count": 247, "id": "5e7e1689", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["closer\n"]}], "source": ["# torch.manual_seed(123)\n", "next_token_id = torch.multinomial(probas, num_samples=1).item()\n", "print(inverse_vocab[next_token_id])"]}, {"cell_type": "code", "execution_count": 248, "id": "5538bb65", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["73 x closer\n", "0 x every\n", "0 x effort\n", "582 x forward\n", "2 x inches\n", "0 x moves\n", "0 x pizza\n", "343 x toward\n"]}], "source": ["def print_sampled_tokens(probas):\n", "    torch.manual_seed(123)\n", "    sample = [torch.multinomial(probas, num_samples=1).item() for i in range(1_000)]\n", "    sampled_ids = torch.bincount(torch.tensor(sample))\n", "    for i, freq in enumerate(sampled_ids):\n", "        print(f\"{freq} x {inverse_vocab[i]}\")\n", "\n", "print_sampled_tokens(probas)"]}, {"cell_type": "code", "execution_count": 249, "id": "ba9861a9", "metadata": {}, "outputs": [], "source": ["def softmax_with_temperature(logits, temperature):\n", "    logits = logits / temperature\n", "    return torch.softmax(logits, dim=-1)\n"]}, {"cell_type": "code", "execution_count": 250, "id": "1d39b7e2", "metadata": {}, "outputs": [], "source": ["temperature = [1, 0.1, 5]\n", "scaled_probas = [softmax_with_temperature(next_token_logits, T) for T in temperature]"]}, {"cell_type": "code", "execution_count": 251, "id": "bf239366", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([6.0907e-02, 1.6313e-03, 1.0019e-04, 5.7212e-01, 3.4190e-03, 1.3257e-04,\n", "        1.0120e-04, 3.5758e-01, 4.0122e-03])"]}, "execution_count": 251, "metadata": {}, "output_type": "execute_result"}], "source": ["scaled_probas[0]"]}, {"cell_type": "code", "execution_count": 252, "id": "9c027906", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([1.8530e-10, 3.5189e-26, 2.6890e-38, 9.9099e-01, 5.7569e-23, 4.4220e-37,\n", "        2.9718e-38, 9.0133e-03, 2.8514e-22])"]}, "execution_count": 252, "metadata": {}, "output_type": "execute_result"}], "source": ["scaled_probas[1]"]}, {"cell_type": "code", "execution_count": 253, "id": "117ffd04", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0.1546, 0.0750, 0.0429, 0.2421, 0.0869, 0.0454, 0.0430, 0.2203, 0.0898])"]}, "execution_count": 253, "metadata": {}, "output_type": "execute_result"}], "source": ["scaled_probas[2]"]}, {"cell_type": "code", "execution_count": 254, "id": "805d3e96", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAeoAAAEiCAYAAAA21pHjAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAPrBJREFUeJzt3QeUU9X2P/BNE6RJ7yBNQaRJBykqHRRBUZqAtCcCgiIoIFWqNIHHUKQJ0uUJKkoRnnSQXqQqRXj0jgICwv2v7/6tm38SMsPMJJmcm/l+1spi5s5Mcidksu85Z5+9E1iWZQkREREZKWGoT4CIiIgix0BNRERkMAZqIiIigzFQExERGYyBmoiIyGAM1ERERAZjoCYiIjIYAzUREZHBEks88+DBAzlz5oykSpVKEiRIEOrTISKieMiyLPnzzz8lW7ZskjBh1GPmeBeoEaRz5swZ6tMgIiKSU6dOSY4cOaL8nngXqDGStp+c1KlTh/p0iIgoHrpx44YOGu2YFJV4F6jt6W4EaQZqIiIKpegswTKZjIiIyGAhDdTr1q2TV155RRfTcVWxZMmSR/7MmjVrpESJEpI0aVLJnz+/fPnll3FyrkRERPEuUN+8eVOKFSsmERER0fr+48ePS926deXFF1+U3bt3y/vvvy9t27aVFStWBP1ciYiIQiGka9S1a9fWW3RNmjRJ8uTJI6NGjdLPn3nmGdmwYYN8/vnnUrNmzSCeKRHF9TbKu3fvhvo0iGItSZIkkihRIgkERyWTbd68WapVq+ZxDAEaI+vI3LlzR2/umXZEZC4EaMyeIVgTOVmaNGkkS5YsftfscFSgPnfunGTOnNnjGD5H8L19+7Y8/vjjD/3M0KFDZcCAAXF4lkTkTxGIs2fP6kgEW1ceVQiCyNTX8a1bt+TChQv6edasWeNPoI6Nnj17SteuXR/au0ZE5vnnn3/0DQ4JpsmTJw/16RDFmj1wRLDOlCmTX9PgjgrUmEI4f/68xzF8jv3QvkbTgOxw3IiM0v+JKL52XeKr+/fv67+PPfZYqE+FyG/2xea9e/f8CtSOmlcqX768rF692uPYTz/9pMeJKHywDj+FgwQBeh2HNFD/9ddfus0KN0ACCT4+efKka9q6RYsWru9v3769HDt2TD766CM5dOiQTJgwQRYuXCgffPBByH4HIiKiYAppoN6+fbs899xzegOsJePjvn376udIKrGDNmBr1g8//KCjaOy/xjatqVOncmsWERGFrZCuUb/wwguaHRcZX1XH8DO7du0K8pkRkUly9/ghTh/vxLC6AZve7Nevn/Tv31/CSe7cuXVbbFRbY03XuXNn2bhxo/z6669ak8Oe2TWRo5LJiIhMg5k/24IFC3RG8PDhw65jKVOmFCfAoAnJfIkTJ47TPfOhTBxs3bq1/PLLL7J3714xmaOSyYiITNyNYt+eeOIJHWG7H5s/f76O2JIlSyYFCxbU3BrbiRMn9PuRa1OpUiXdvVK6dGk5cuSIbNu2TUqVKqWBHhUcL1686Pq5t99+W+rXr681IjJmzKg7X5DD417NDQVjUEcCS4a4XywXLlq0yKNvAh572bJlUrJkSd0dg0qPR48elVdffVVrVOCxcT6rVq3ymNX8448/NDcIP2/PKGDWoHjx4h7PzZgxY3T07X3egwcP1i14BQoUcLUdfvPNN7VASLp06fTx8dwE07hx46Rjx46SN29eMR0DNRFRkMyZM0dH2AhMBw8elCFDhkifPn1k5syZD02P9+7dW3bu3Kkj2qZNm2rS7NixY2X9+vXy+++/u3J3bNgBg/tEwJ03b5588803HsWdEKRnzZqlpZf379+vgfWtt96StWvXetxPjx49ZNiwYXpfRYsW1STfOnXq6P1jmbFWrVraPMnOF8Lj5MiRQz799FOdTXCfUYgO3C9mHJBrtHTpUt26hDwj9GXG74rpaFwg4HGjKiObMmXKKG+4cAkXnPomIgoSBGAkvb722mv6OUa3Bw4ckMmTJ0vLli1d39etWzdXUmyXLl2kSZMmGtCef/55PdamTZuHcnYwZTx9+nTdq/vss89q4OzevbsMHDhQgx8uCjAStrevYuSIETMeu0qVKq77wc9Vr17d9TlGtBh923B/ixcvlu+++046deqkX8eeYARWzBjEVIoUKTQJ2J7ynj17to7+ccwenc+YMUNH17gIqVGjhs/7edSaMmYZwgUDNRFRkLoDYhoZQbZdu3Ye1dcwRe4OI1mbXSa5SJEiHsfscpQ2BFP36m0IyBgNYxoZ/6LCm3sABoxQ7V02Nkyvu8PPYhobO2wwWsb5okSz+w4cf+D3cl+X3rNnj84YIPC7+/vvv/X5iwzaHMcXDNREREGAgAdTpkyRsmXLenzNu0oVOi3Z7FGl97GYNCmxHxvBNnv27B5f867UiBGuO4zuMS09cuRIDYZY327YsOEju5mhLrv3Lh6M7L15Px7OFWvkWCbwhvX3yDwqSQ/T/Jj2DwcM1EREQYBRMBKmUKSpWbNmAb9/jETdmxFt2bJFgxd6GWB6GgEZo2D3ae7owBoxkr4aNGjgCqTeiV0YEdvlXt2DKhonIVjbFxvR2fJUokQJzZZHPeyYTFfv5tQ3ERH5C8ld2K+LqW4kR6HlLgo9Xb161aNZUGxghItpdSShIZBiPRxryBjZYhoZI2MkkGEkXrFiRbl+/boGYQQw9/Vxb0899ZQmjCGBDAEXyW/eo3lkcq9bt04aN26sFwQZMmTQbHBkpg8fPlxH4MuXL9eM8kcFTFzEjBgxQjO9sV6ORDVkleMckFCXI0eOoEx9Y7odFyG4uMAFjx34CxUqZFyteWZ9ExEFSdu2bTVJCslRWJvF6BZJYUgq81fVqlU1qFauXFkaNWok9erV8yisgiQwBFlkf2N7GC4UMBX+qMcePXq0pE2bVipUqKDBGkluGPW6Q0DFxUG+fPlc09N4DGw9i4iI0PXzrVu36sXCo2CdHUE/V65cmnSH+8EFCNaogzkqbtu2ra7XI7kO2+HsKplnzpwR0ySwoioNFobQ5hJXt7i6DKepEXIYds/yCW/OqPmPYIJ9x+QbpqavXbsmS5YsCfWpUCxfzzGJRRxRExERGYyBmoiIyGBMJiMichhfDYsofHFETUREZDAGaiIiIoMxUBMRERmMgZqIiMhgDNREREQGY6AmIiIyGAM1EZEfUA87qpt7Wc9wgVrfY8aMESc7efKk1K1bV0uYoiEIenmjpWdUBg8erKVV8TPolx1XuI+aiJxdcjUojxf9Mq7o2WxDF6i+ffvK4cOHo92O0RSoJo2OWIkTx11YQGORUDTAuH//vgbpLFmyyKZNm/T/sEWLFtpadMiQIVGe7xtvvKG9v6dNmxZn58sRNRGRH/Bmb99QuxmjaPdj8+fP10YTqPVcsGBBbVxhQ2MLfP/ChQulUqVK2rKydOnS2iRi27ZtUqpUKQ30tWvX1s5U7rW+69evr9250BQDtaLbt2/v0TMaHa/QkAN1pnG/aJSxaNEi19fXrFmjj40OV+gHjS5YGzZskKNHj2onK7TpxGPjfFatWuX6OXTJQncrdOayZw0AMwfFixf3eG4w6sbo2/u8MTJFC9ACBQro8VOnTsmbb76po1S06MTje7fWDKSVK1fKgQMHZPbs2XrOeH7RxAQNRaLqu43nG783GqzEJQZqIqIgmTNnjo6wEZgOHjyoozV0tJo5c6bH96FFJdpV7ty5U0e0TZs21RaPY8eOlfXr12tLRtyPu9WrV+t9IuDOmzdP20IikNgQpGfNmiWTJk2S/fv3a4B56623ZO3atR7306NHDxk2bJjeV9GiRbX1Y506dfT+d+3apV230EULU8WAx0HrSXTQwkjUfUYhOnC/mHH46aefZOnSpXLv3j3t0IXWnPhd0YoTFwh43KiCZsqUKaO84cIlMps3b9Zgi4sRG84BjTLwXJmGU99EREGCADxq1Cht3wgY3WIkh9aK7j2h0Q4SgQK6dOkiTZo00YD2/PPP6zG0ffQuG4op4+nTp+t66bPPPquBE+usGBki+OGiACNhTNNC3rx5dcSMx0a7TRt+rnr16q7PMaLF6NuG+1u8eLF899132u8aX0+UKJEGVswYxFSKFCm09ac95Y1RLUb/OGaPztEWFKNrXITUqFHD5/3Y/aMjE1VHKvSgdg/SYH+Or5mGgZqIKAhu3ryp08gIsu3atXMdR8ISpsjdYSTrHTDcp1dx7MKFCx4/g2CKIG1DQMZoGNPI+PfWrVseARgwQkXPZXeYXneHn8U0NnpXY7SM8719+7ZrRO0v/F7u69J79uzRGQMEfu8WkXj+IpM/f36JLxioiYiCAAEPpkyZImXLlvX4Gkak7pDEZLNHld7HMOqM6WMj2GbPnt3ja1iL9h7husPoHtPSI0eO1GCI9e2GDRtGOQ0NCRMm1IQ0dxjZe/N+PJwr1sixTOAN6++ReVSSHqb5Me3vC2YCtm7d6nHs/Pnzrq+ZhoGaiCgIMApGwtSxY8ekWbNmAb9/jEQx0kUghS1btmjwypkzp05PIyBjFOw+zR0dWCNG0leDBg1cgdQ7sQsjYmROewdVTBsjWNsXG4+anoYSJUpotjy2SEU1XR3IqW/MPiBvALMUeFzAxQl+plChQmIaBmoioiBBclfnzp11qhvJUXfu3JHt27fL1atXpWvXrn7dN0a4mFZHEhoCKdbDsYaMkS2mkTEyRgIZRuIVK1aU69evaxBGMHJfH/f21FNPacIYEsgQcJH85j2aRyb3unXrpHHjxnpBkCFDBs0GR2b68OHDdQS+fPlyzSh/VPDFRcyIESM00xvr5UhUQ1Y5zgEJdTly5Aj41DfWvRGQmzdvrueLCww8jx07dnTNOGDEjS1byBWwZyVw4XPlyhX9Fxcq9sUCziWY2/BCnvWNdHj8p2PrAqaHvKcjvCHdHyn9uIrElSNeiFjLICIyTdu2bTVJCslRWJvF6BZJYUgq81fVqlU1qFauXFkaNWok9erV8yiugiQwBFlkf2N7GC4UMBX+qMcePXq0pE2bVgt7IFgjyQ2jXncIqLg4yJcvn2t6Go+BrWd4T8f6Od7LcbHwKFhnR9DPlSuXJt3hfnABgvf1mIywYwJLD8g4x78YXWOaHEEZv5cNa/zITnefvkfmPdb4cVGEmQZ8jBsuvoIpgeW9qBCHMN2BJwfrCAjSCMJff/21Pjn2dIS7uXPnSuvWrTXTES8i7DXEFA2u6vDiig6k3+PqFleXwXoREPlVwCMGxTbCDd6cjx8/rsEEF+/kG973rl27JkuWLAn1qVAsX88xiUUhHVEjuCIbslWrVjoNgYCNqysEYl9QQQbbFbDHEKNwTF9gG8OjRuFEREROFbJAjfWVHTt2SLVq1f7/ySRMqJ9jM7ovGEXjZ+zAjCSNH3/8UTfnExERhaOQJZNdunRJF+N9bTo/dOiQz5/BSBo/h8QIzNhjfx+qz/Tq1SvSx0HyBm7u0w1ERE7mXfyEwlvIk8liAlVqUG0HCQsotYesQCRHIGkiMkikwDqAfUMCGhERkVOEbESNdH5k3NmbzG34PLIN58hgRDo9MikBWZSo/vOvf/1LPvnkE50699azZ0+PbRAYUTNYExGRU4RsRI0N86hGgz1qNuzVw+d2bVpvSJf3DsZ2hZ/IktexJw4Zde43IiIipwhpwROMdLHxHrVmy5Qpo9uzMEJGFjhg6xY2mmP6GrCnD5ni2LeG7VyoD4tRNo57l+QjIiIKByEN1Nikj0o22ESOyjDoC4pqNnaCGaq/uI+gUTkGlXLw7+nTp3WjPYI0SsERERGFo5AWPAkFFjwhI7DgiU8seELh5O9wKHhCREREUWOgJiLyA5bjorq5198OF6gMiZwiJ0vg4/9q/vz5YiJ2zyIi4xWZWSROH29fy33R/t6zZ8969C9Azg36FdiC2VUpkLAKiiJUiRMnjtMKldgBFCozZszQZiW2NGnSiIk4oiYi8gPqPtg3rDliZOZ+DKM0dITCGmXBggW1YJMNHajw/QsXLpRKlSppV8DSpUtrw6Ft27bpjhgE+tq1a2virXtTjvr162sbTSTVYo0TVRoR+Ny3u2LHDNZHcb/oaLVo0SKPAlJ4bLSixFZZbGXdsGGDHD16VFtOIqkXj43zWbVqlevn0M4SbSjRudAeiQJmDpAQ7A6jboy+vc8bCcDo1Y1OiHDq1Cl58803NVCilzYe37sHdjDg8dz/r0zNi2CgJiIKkjlz5ugIG4Hp4MGDWlkRW0pnzpzp8X1om4jdLKi4iBEtyiWjF/PYsWNl/fr1uhUV9+MONSdwnwi48+bN00qNCNw2BOlZs2Zps6P9+/drYEU7x7Vr13rcT48ePWTYsGF6X0WLFtX2jeifgPvftWuXjjixuwa7cACPgx7RaAmJ2QT3GYXowP1ixuGnn37SVpNoI4lWmuihjd8VPbNxgYDHdb/w8IbvieqGC5dHQf9pFN/C9mA0gzI1t5pT30REQYIAPGrUKO2zDBjdHjhwQCZPnqw1JGzo24xgBV26dNGugAho6BYI6M/sXd8bU8YILug4+Oyzz2rg7N69u5ZURvDDRQFGwnYBqbx58+qIGY+Nvtg2/Fz16tVdn2NEi9G3Dfe3ePFi+e6776RTp076ddStQGCNrIpkVFKkSKE9uu0p79mzZ+voH8fs0TmmpDHaxUVIjRo1fN7P7t27o3ycR2VS4/d+6aWX9PlbuXKldOjQQS9SOnfuLKZhoCYiCgIUb8I0MoIs2vna0EwIU+TuMJK12XUkUCLZ/diFCxc8fgbBFEHGhoCMQINpZPyLSo7uARgwQkXBKHeYXneHn8U0NvooYLSM8719+7ZrRO0v/F7u69J79uzRGQMEfu+tTXj+IpM/f37xB2Y2bHhO8P81YsQIBmoiovgCAQ+mTJmilRTdeVdSTJIkietje1TpfQyjzpg+NoItqju6w1q09wjXHUb3mJYeOXKkBkOsbzds2DDKaWhAcSrvqWOM7L15Px7OFWvkWCbwhvX3yDwqSQ/T/Jj2jy78H2H2AN0WvZ+jUGOgJiIKAoyCkTB17NgxadasWcDvHyNRjHQRSGHLli0avNB0CNPTCDYYBbtPc0cH1oiR9NWgQQNXIPVO7MKIGBni3kEVFSYRrO2LjUdNT0OJEiU0Wz5TpkwxKkK128+pb1/3lzZtWuOCNDBQExEFCZK7MJWKqW4kR2G0tn37drl69apHV7/YwAgX0+pIQkMgxXo41pAxssU0MkbGSCDDSLxixYpaAQtBGAHMfX3c21NPPaUJY0ggQ8DFFLH3aB6Z3OvWrZPGjRtrYENCFrLBkZk+fPhwHYGjHDQyyh8VMHERgylnZHpj3RiJasgqxzkgoS5HjhwBn/r+/vvvtVNjuXLlNNMbMwhY08dzZiJmfRMRBQla8iJJCslRWJvF6BZJYUgq81fVqlU1qFauXFn7JtSrV8+juAqmcRFkkf2N7WG4UMBU+KMeG42PMLKsUKGCBmskuWHU6w4BFRcH+fLlc01P4zGw9SwiIkLXz7du3RqtwId1dgT9XLlyadId7gcXIFijDlaZ5yRJkuh5Yl0fW8qQYIffGxc7JmKtb6JQYK1vn1jrO3owNX3t2jVZsmRJqE+FosBa30RERPEAAzUREZHBmExGROQw3sVPKLzFakT9888/B/5MiIiIKDCBGtmDyPYbNGiQVsEhIiIigwL16dOndb8eOrGgfizS99H95VGVa4iIoiOebUahMGUF6HUcq0CNze3YSI9KLr/88os8/fTTWtAcVXiwuR8Vc4iIYsourcmLfgoHt27deqgcbEiSybARHh1U0qdPr63S0M0Fm96xkRx1VtHVhYgoOtDiEQUwUOEKb26oskXkxJE0gjQaqaALmHdt9zgL1Ci2/u2332pgRvk1dGAZP368tmfDHxnK2r3xxhva0o2IKDpQsjJr1qxaJAJlJImcDEE6Nq1AAxKo33vvPW1UjquG5s2ba23XwoULe3RHQecVTIUTEcUEGj6gNCanv8nJkiRJ4vdI2q9AjVHyv//9b63LGlmnEaxjcxsXEcUGprxZQpTo/8RqAQiFyzGt7R2k0WAcxdXttaaYtlcjIiKiAATqF198Ua5cufLQcRQXx9eIiIgohIHavTG4u8uXL+v6NBEREUncr1FjTRoQpNFmzX3q+/79+7J3717tYUpEREQhCNTonWmPqFOlSiWPP/64R6ZmuXLlpF27dgE6NSIiIopRoJ4xY4b+mzt3bunWrRunuYmIiEzN+g5UkI6IiNDAj60YZcuWla1bt0b5/deuXZOOHTtqUQRMvaN86Y8//hiQcyEiInLsiBqlQlevXi1p06aV5557zmcymW3nzp3Rus8FCxZI165dtdQogvSYMWO0wcfhw4clU6ZMD30/CiBUr15dv4aGINmzZ9fqRaj+QkREFK8D9auvvupKHqtfv35AHnz06NG6pt2qVSv9HAH7hx9+0LKkPXr0eOj7cRzbwjZt2uQqco7ROBERUbhKYIWonxxGxyi+j5Gxe+Bv2bKlTm+jjri3OnXqSLp06fTn8PWMGTNK06ZN5eOPP460VNudO3f0Zrtx44bkzJlT93ynTp06SL8d0SP0fyKKr12PyzMhohBALEKCdnRiUcha01y6dEm3dGXOnNnjOD4/d+6cz585duyYBnb8HNal+/TpI6NGjZJBgwZF+jhDhw7VJ8O+IUgTERGF3dQ31qajWpd256tqWSA8ePBA16e/+OILHUGXLFlSTp8+LSNGjNAEN1969uyp6+DeI2oiIqKwCtRI9AokNO1AsD1//rzHcXweWVswZHp7dyR55plndASOqXTs5faGdfXIGocQERGFTaDG2nEgIahiRIxMcnuNGiNmfN6pUyefP/P888/L3Llz9fvshvJHjhzRAO4rSBMRETldtNeoMWXs/nFUt+jClPSUKVNk5syZcvDgQXn33Xfl5s2brizwFi1a6NS1DV/HtHqXLl00QCNDfMiQIbqvmoiISOL7GvXZs2d1jRj7ln2tV9vNOpDsFR2NGjWSixcvSt++fXX6unjx4rJ8+XJXgtnJkyddI2fA2vKKFSvkgw8+kKJFi+o+agRtZH0TERHF6+1Za9eu1aln9JnGx1ExuQ91TFLiifyRu8cPkX7tRLKmkf8gt2cRhb0bMYhF0R5RuwdfkwMxERFRvG3K4e7q1asybdo0XVuGQoUK6doyCpIQERFRYMSq4Mm6deu0dOe4ceM0YOOGj/PkyaNfIyIiohCOqJFljUSwiRMnuvY0I4GsQ4cO+rV9+/YF6PSIiIjit1iNqH///Xf58MMPPQqP4GNst8LXiIiIKISBGi0v7bVpdzhWrFixQJwXERERxWTqe+/eva6PO3furPuXMXouV66cHtuyZYtERETIsGHDgnOmRERE8VC091Gj8AiKmTzq22NS8CQUuI+a4gr3URNRnO6jPn78eHS/lYiIiAIk2oH6ySefDNRjEhERUbALnsCBAwe0HjdaTLqrV6+eP3dLRERE/gTqY8eOSYMGDXS/tPu6td2ow+Q1aiIiorDfnoWMb1Qhu3DhgiRPnlz279+vFclKlSola9asCfxZEhERxVOxGlFv3rxZ/vvf/0qGDBk0Gxy3ihUrytChQ3Xr1q5duwJ/pkRERPFQrEbUmNpOlSqVfoxgfebMGVfC2eHDhwN7hkRERPFYrEbUhQsXlj179uj0d9myZWX48OHy2GOPyRdffCF58+YN/FkSERHFU7EK1L1795abN2/qx59++qm8/PLLUqlSJUmfPr0sWLAg0OdIREQUb8UqUNesWdP1cf78+eXQoUNy5coVSZs2rSvzm4iIiEK8jxpOnTql/+bMmTMAp0NERER+J5P9888/0qdPH61Tmjt3br3hY0yJ37t3LzZ3SURERIEaUb/33nvyzTffaBJZ+fLlXVu2+vfvL5cvX5aJEyfG5m6JiIgoEIF67ty5Mn/+fKldu7brWNGiRXX6u0mTJgzUREREoZz6Tpo0qU53e8N2LWzTIiIiohAG6k6dOsnAgQPlzp07rmP4ePDgwfo1IiIiiuOp79dee83j81WrVkmOHDmkWLFi+jkKoKCLVtWqVQN0akRERBTtQI2sbnevv/66x+fcnkVERBTCQD1jxowgPDwREREFreDJxYsXXU04ChQoIBkzZvTn7oiIiCgQyWSo8926dWvJmjWrVK5cWW/ZsmWTNm3ayK1bt2Jzl0RERBSoQN21a1dZu3atfP/993Lt2jW9ffvtt3rsww8/jPH9RURE6HavZMmSaTeurVu3RuvnsJcbtcXr168fi9+CiIgoTAP1f/7zH5k2bZoWPEmdOrXe6tSpI1OmTJFFixbF6L7QbQuBv1+/frJz507NIkfTjwsXLkT5cydOnJBu3bpp1y4iIqJwFatAjentzJkzP3Q8U6ZMMZ76Hj16tLRr105atWolhQoVkkmTJkny5Mll+vTpkf7M/fv3pVmzZjJgwAD2vyYiorAWq0CN+t4YAf/999+uY7dv39bAadf+jg7su96xY4dUq1bt/59QwoT6OWqHRwY9sHFRgDXxR0Ehlhs3bnjciIiIwjrre8yYMVKrVq2HCp5gjXnFihXRvp9Lly7p6Nh7dI7P0ePalw0bNui0++7du6P1GEOHDtULCCIiongTqIsUKSK//fabzJkzxxVQ0YwD09GPP/64BMuff/4pzZs317XwDBkyROtnevbsqWvgNoyoWZyFiIjCNlCj33TBggVl6dKlurbsDwTbRIkSyfnz5z2O4/MsWbI89P1Hjx7VJLJXXnnFdezBgwf6b+LEiXVPd758+R5qIIIbERFRvFijTpIkicfatD/QaatkyZKyevVqj8CLz32tdeMCYd++fTrtbd/q1asnL774on7MkTIREYWbWE19d+zYUT777DOZOnWqjmT9gWnpli1bSqlSpaRMmTK6/o2CKsgChxYtWkj27Nl1rRlr4IULF/b4+TRp0ui/3seJiIjCQayi7LZt23TUu3LlSl2vTpEihcfXv/nmm2jfV6NGjbQUad++feXcuXNSvHhxWb58uSvB7OTJk5oJTkREFB/FKlBjFOvdPcsf6GEdWR/rNWvWRPmzX375ZcDOg4iIyNGBGuvHI0aMkCNHjuge6Jdeekn69+8f1ExvIiKi+CxGc8qDBw+WXr16ScqUKXXdeNy4cbpeTURERAaMqGfNmiUTJkyQd955Rz9ftWqV1K1bV5PKuI5MRBTecvf4wefxE8Pqxvm5xCcxiq5I7ELzDRtKfaJ71ZkzZ4JxbkRERPFejAL1P//8o1ukvPdVowgKERERhXjq27Isefvttz0qfaH4Sfv27T22aMVkexYREREFKFCjMIm3t956KyZ3QURERMEK1DNmzIjJtxMREZGfmKpNRERkMAZqIiIigzFQExERGYyBmoiIyGAM1ERERAZjoCYiIjIYAzUREZHBGKiJiIgMxkBNRERkMAZqIiIigzFQExERGYyBmoiIyGAM1ERERAZjoCYiIjIYAzUREZHBGKiJiIgMxkBNRERksMShPgEi8lRkZpFIv7av5b44PRciCj2OqImIiAzGQE1ERGQwIwJ1RESE5M6dW5IlSyZly5aVrVu3Rvq9U6ZMkUqVKknatGn1Vq1atSi/n4iIyMlCvka9YMEC6dq1q0yaNEmD9JgxY6RmzZpy+PBhyZQp00Pfv2bNGmnSpIlUqFBBA/tnn30mNWrUkP3790v27NlD8jsQEZFvzLkIgxH16NGjpV27dtKqVSspVKiQBuzkyZPL9OnTfX7/nDlzpEOHDlK8eHEpWLCgTJ06VR48eCCrV6+O83MnIiIK60B99+5d2bFjh05fu04oYUL9fPPmzdG6j1u3bsm9e/ckXbp0QTxTIiKieDj1fenSJbl//75kzpzZ4zg+P3ToULTu4+OPP5Zs2bJ5BHt3d+7c0Zvtxo0bfp41ERFRPJr69sewYcNk/vz5snjxYl2v9mXo0KHyxBNPuG45c+aM8/MkIiJyZKDOkCGDJEqUSM6fP+9xHJ9nyZIlyp8dOXKkBuqVK1dK0aJFI/2+nj17yvXr1123U6dOBez8iYiIwjpQP/bYY1KyZEmPRDA7Max8+fKR/tzw4cNl4MCBsnz5cilVqlSUj5E0aVJJnTq1x42IiMgpQr49C1uzWrZsqQG3TJkyuj3r5s2bmgUOLVq00G1XmMIGbMfq27evzJ07V/denzt3To+nTJlSb0REROEk5IG6UaNGcvHiRQ2+CLrYdoWRsp1gdvLkSc0Et02cOFGzxRs2bOhxP/369ZP+/fvH+fkTERGFdaCGTp066c0XFDhxd+LEiTg6KyIiotBzdNY3ERFRuGOgJiIiMhgDNRERkcGMWKOOj1ionoiIooMjaiIiIoMxUBMRERmMgZqIiMhgDNREREQGY6AmIiIyGAM1ERGRwRioiYiIDMZATUREZDAGaiIiIoMxUBMRERmMgZqIiMhgDNREREQGY1MOIvIbm8xQOCli2OuZI2oiIiKDMVATEREZjFPf5NjpICKi+IAjaiIiIoMxUBMRERmMU99+yt3jh0i/dmJY3Tg9FyIiCj8cURMRERmMgZqIiMhgnPqmsMZMdQqn14YTz5n8xxE1ERGRwRioiYiIDMZATUREZDAjAnVERITkzp1bkiVLJmXLlpWtW7dG+f1ff/21FCxYUL+/SJEi8uOPP8bZuRIREcWrQL1gwQLp2rWr9OvXT3bu3CnFihWTmjVryoULF3x+/6ZNm6RJkybSpk0b2bVrl9SvX19vv/76a5yfOxERUdgH6tGjR0u7du2kVatWUqhQIZk0aZIkT55cpk+f7vP7x44dK7Vq1ZLu3bvLM888IwMHDpQSJUrI+PHj4/zciYiIwnp71t27d2XHjh3Ss2dP17GECRNKtWrVZPPmzT5/BscxAneHEfiSJUuCfr5ERORD/yci/1qeXHF5JmEppIH60qVLcv/+fcmcObPHcXx+6NAhnz9z7tw5n9+P477cuXNHb7br16/rvzdu3AjAbyDy4M6tSL8W1WPcv30/Vj8XCIX7rYj0a78OqGnkOcdWKM85ytdGAsvY5zmy1wdfG6EX6nOO7DXN13PM2fdjWZE/dy5WCJ0+fRpnaG3atMnjePfu3a0yZcr4/JkkSZJYc+fO9TgWERFhZcqUyef39+vXTx+DN95444033sSw26lTpx4ZK0M6os6QIYMkSpRIzp8/73Ecn2fJksXnz+B4TL4f0+ruU+UPHjyQK1euSPr06SVBggQSSLhCypkzp5w6dUpSp04tTsBzjhs857jBc44bPGf/YST9559/SrZs2R75vSEN1I899piULFlSVq9erZnbdiDF5506dfL5M+XLl9evv//++65jP/30kx73JWnSpHpzlyZNGgkmvAhMeCHEBM85bvCc4wbPOW7wnP3zxBNRrO2bVOsbo92WLVtKqVKlpEyZMjJmzBi5efOmZoFDixYtJHv27DJ06FD9vEuXLlKlShUZNWqU1K1bV+bPny/bt2+XL774IsS/CRERUeCFPFA3atRILl68KH379tWEsOLFi8vy5ctdCWMnT57UTHBbhQoVZO7cudK7d2/p1auXPPXUU5rxXbhw4RD+FkRERGEaqAHT3JFNda9Zs+ahY2+88YbeTIMpdhRu8Z5qNxnPOW7wnOMGzzlu8JzjVgJklMXxYxIREZFTKpMRERFR5BioiYiIDMZATUREZDAGaiIiIoMxUMfSP//8I7NmzXqoShoREVEgMevbD2jHefDgQXnyySfFKVBcBr28K1euLE6SN29e2bZtm5Z+dXft2jVtc3rs2DEJte+++y7a31uvXr2gnkt8hkY/+/bt07/LtGnThvp0HCsmzSdMqfTlbd26dRIVp7wPGrGP2qlQSW337t2OCtToHoY2ojhnVH9D4EblN9OdOHFC34C9oTPa6dOnxQR2GVwbasm7Xwe715b39buYYObMmVqDH1X/4KOPPtKqf+gVP2/ePCNf6ygnXKRIEb0AxfOKyoWbNm3SC+mlS5fKCy+8EOpTdCSUWo5uPwRTX88v+Pi/d8LfoTcGaj906NBBS6CiyDtqlqdIkcLj60WLFhXToIobKsF99dVX+qaMAgAI3HiTe/XVVyVJkiRiEvdR6ooVKzxq4+KPDHXfc+fOLSZAnXrbqlWr5OOPP5YhQ4a46tCjlzoq6uGYqXBuEydOdJ1vRESEfP755xrwPvjgA/nmm2/ENIsWLZK33npLP/7+++/l+PHj2iYXr/FPPvlENm7cKCbCeS9cuFCrL969e9fjazt37pRQ+/nnnz0ulHv06CFvv/22x+sZ7yF2eWcTXb161ePze/fuya5du6RPnz4yePBgcYwYdKUkLwkSJHjoljBhQte/TrBjxw6rU6dOVrJkyawMGTJY77//vnXkyBHL5OfYvj322GPW008/bX3//feWaZ599llr/fr1Dx1ft26dVbBgQctUjz/+uPXHH3/oxx999JHVvHlz/fjXX3/V14eJkiZN6moV2K5dO6tLly768bFjx6xUqVJZJho7dqyVMmVK/dvD6/idd96xqlWrZj3xxBNWr169LNO89NJLD7UXhjlz5lhVqlSxnGbNmjVWiRIlLKdgMpkfcOXufcNaqf2v6c6ePaudx3BDu9E6dero2h6mOTGKMmWUihumXDETYH+OG6a9Dx8+LC+//LKY5ujRoz67tGFGAKMTU6VMmVIuX76sH69cuVKqV6+uHydLlkxu374tJkJfgAMHDugMC/oE2Od869YtfV2baMKECbqk8O9//1u7CGKJAX+HnTt31uUp02D0jMZJ3nBs69at4jSZM2fW9w7HCPWVAsWtu3fvWosWLbLq1q1rJUmSxCpZsqQ1ceJE6/r1667v+eabb6w0adJYJp0zruhNGuk/SqVKlazq1atb586dcx3DxzVq1LAqV65smapp06Y60mjTpo2VPHly69KlS3r822+/1VkCE/Xr109HopipyJUrl/X333/r8WnTplnlypWzTJ25OHHihH6cMWNGa/fu3foxXuPp0qWzTIOZq+7duz90HMfwNVPt2bPH44bnedmyZToL8Pzzz1tOwTVqP2EdbNKkSTqKxlUnRn5o1ZknTx5d8zVN1qxZdTTapEkTvRJGtzJvL774YtB7dscE1s337t0rTjJt2jR57bXXJFeuXNqsHpDLYHd7MxXWpLGOjnP9z3/+48qy37Fjh75mTNS/f3/tnodzRrMeu+kCRtNYVzVRlixZ5MqVK/p+gdfIli1bpFixYvo+YuJGHMywvf7667Js2TIpW7asHsP7x2+//aavE1MVL178oaROKFeunEyfPl2cgtuz/ICkG7TnRNYpEhN+/fVX3Ub05ZdfapKFezKGSRcWeDPDVKaTIJEJb8DDhg0Tp8CfFqYzkdgEzzzzjCbuRTeTlmLu77//dsRru23btnoBh2ROXBx1795dnn/+edm+fbte4OFCzzT/+9//9D0PW1Lt13P79u1dF6Im+uOPPzw+R8vkjBkzOuI14o6B2g9Yy0WWLLblpEqVSvbs2aOBGgEb2wIuXbokJkHG4+OPP65bypzWv/u9997TAjMYkfrKsB89erSYwsnPM6xfv14mT56seRZff/21bt/DBR5miSpWrCimwdo0/g4xs4UCREeOHNG/Q2T2YkcAdjSYxs6zSJz4/yY158+fr1vK8Pp+5513dN3apNdzrVq19PnF+VHcYzKZHzBN9dxzzz10HCO/mzdvimkwhYxpNqfsHXSHix8UNsEFEd6IscXCviEgmsTJzzOmMWvWrKkXGtgihIQ9QIKTqdvKMJuFWazhw4d7BDhcJE2dOlVMhJGdHaShcePGMm7cOL0gNSlIO3Xpyd3atWvllVdekfz58+sNxYZwMeoooV4kd7JnnnnGWrJkiX6MrRZHjx7Vj8eNG2c999xzlommTp1q1alTx7p8+XKoTyWsOfV5Ll68uDVz5syHXtM7d+60MmfObJkoX7581qpVqx4654MHDxqVFOkuT5481ttvv+1KfLNdvHhRv2YabNv8+OOPLaf56quvrMSJE1tvvvmmbonDDR8jkRZby5yCyWR+QLGTjh076roYVhCQXIHqTSgAYOqV/Pjx4+X333+XbNmyaSKL9xSyCYUWorNWBjly5BBTOfV5xpYVX2UVsa0M5VpNhMp0GCl5w9Qypm1NhC16GFFXqlRJi/oguQwwC+O9rmpKbwMkX6GQj+lLT96zLZhpQY6LDVvgcL4DBw6Upk2bihMwUPuZEIIpQmTJYs8m/tPxxjx27FidyjKRd5lLp8Cb7qBBg2TUqFHy119/6TFMg3/44YdafQpTiSZx6vOMgIELDO9qbxs2bNB1X1NzRTCV6V3eFJW/fC1NmQAJhdjz3a1bNw182AlQunRpMX3pCbD05M7k5Mhjx47ptLc3TH/36tVLHCPUQ/pwcfPmTev8+fOhPo2w1aNHD91vOmHCBNeeyIiICD1mYiUnpxoyZIhVqFAha8uWLVrVC9XVZs+erc8zlnRMhOUn7KMeNmyY7v0eMWKE1bZtW634tXLlSstEqKxnv1/gtY191ZimxV57p1Q1dIJ8+fJZkyZNeug4akfkz5/fcgoGaj/cunVLA7QNBQw+//xza8WKFZbJrl69ak2ZMkXfIOw1VJQS/d///meZKmvWrFp0w9ebdLZs2UJyTuHowYMH1qBBg6wUKVK4SrWivGzv3r0tk6E0K0pw4oICQQ/FLEz+O0Qwdr+wR5DG89yqVSsG6gCaMGGCXrC1b9/emjVrlt5QrhVlZ30FcFNxe5YfatSooXsesZcQ63cFChTQjE1sy8IayLvvviumQfYm9vLapSyxJokpTUzfozkAtkCZCPsece5PP/20x3GcP4oamFbeEmuNKBIRWdMFFLswGc4XU+BYZsDUMkqLUuBgqebcuXOSKVMm1zEUTGrQoIGWyjVxxwD2eEf2ejaxWYtt8eLFumTmvv8b+9ZNLEgVqVBfKThZ+vTptVkBYIRatGhR6/79+9bChQuNbbxQtWpVVylA9wzZjRs3Wk8++aRlqjJlyljvvffeQ8fR1KBs2bKWafr06aOzACNHjtSR0sCBA7UsJ14zyDylwMHz+vPPP1vhAFPfaBhhmnnz5mmm9Msvv6wjVPyL0qFYckD2uqlatGhhrV271nI6BuoAdRp64403rP79++vHJ0+e1K+ZKHXq1Nbvv//+UKDGtD2mg0yFNy9Mx2JLXOvWrfWGj/E7YNrTNHnz5rWWLl2qH+Mc7eccQbpJkyaWqf766y+d5i5fvryu72GrkPvNRPXq1dPXbo4cOaxu3bpZu3btskw3YMAAa/Xq1T6ff3zNNEWKFLHGjx/v8b6BZRJ0K+vbt69lqldffVUvMLAePXjwYOv06dOWEzFQ+/nixRsvAjMC4KZNm/T49u3bjd1zijU87In1DtRIusEbncnwR4bEsddee01vn3zyibF/eEhqsi/ismTJojkAgOcbrxVTNW7cWGcC0OIS+RZjxozxuJnqypUr1uTJk7XZAtZ4kRCHN+bjx49bJrLbtI4aNcrjuKnJZHg9288lmobs3btXPz5w4IC+vk124cIFfZ4x44k91bVq1dJZTzT7cQoGaj98/fXXerWGPywksrhnzuLFYOo0Yf369fVFikCNnr0IKCjQYvfxNUWDBg1cXb1QhMO7OITJMC2IzGlAYtPQoUP14/nz5+vFkqkwlblhwwbLydCbevjw4br8lChRIsvUQI3XApZCMHV8584dowN19uzZXcEZAxS7NzUGJyZfeHrDBTOWy7Achf7qKOTihK58DNR+Onv2rI5QsTZt++WXX7QqkomuXbumFxWo2IQ3sZw5c+rFBlovYtrNJDivM2fO+MySNR2qOGFEB3hDxpU8pt8wijK5wlPu3Ll1lORUuABdvHix9frrr+ubsak7AuztWVgSwRIOlhrwuamBGss19uj/008/1YtNbIFDXgsuqJ3gzJkzuoWvQIECuoyG9Wvk7OBvc/To0ZbJmPUdj6pleRewQBY1snpRyACZ4KYpWrSonhvabrZq1UprIadOndrn97Zo0UJMhjaGdtMFXwUYTDF79mz59ttvtftb8uTJxSnQqW7u3LlaqxzFcbAbo1mzZvLSSy8ZWZADLTjPnj2rWd83btyQN998U/bv36+NL1CMw7Ssb+xSQAVGFHTC84tqX/brGTtG0qZNKya6d++eVn6bMWOGrFy5Ut9TUKgKxans9xJkhbdu3VquXr0qpmKgjkfVsgA9e01uS+du48aN+lwePXpU3yjw3Pp608Ux07c7mQzVu9yfV2zLwtsCqpOhIYPppU/R3Qv//+jwhOCMCyG7J7VTtmfhvQTtctFGEh+bFqidKkOGDPp8opd6u3btdCunN2ytxd8AmiyZiiVE/YBgjL6x6JGMXrL2SBWN7HH1iTqzpsGbL1oVvvXWW9KwYUNjr4QBzylGovYbG0oXuu87NRm6Z6HVaZUqVfTffPnyiamcWu7Uhr839FhPkyaNOAVGeKhlYMPrGzNGCBjr1q0T02DGCjNbqANv8mvZG2oZ4LURVf9pvG5MDtLAEbUfMA1kT1W5w9Rhhw4dtFmAadAWElOE6H+LwgoYhSBomzgKwfQl2hdiigpTsZgeRG11J8AUMt5w16xZoyNUjPoQtO3Azb6+weG0JSinwHQxXs/ur2X7QpSv5eBjoI5H1bLc4b8dQcR7XQ8dckyBKm/oJJQ1a1aPNT2nwXmjJ+7SpUtlwYIFRk9tbtu2Tc+vbNmyHsd/+eUX/T8oVaqUmMYpS1AYMf/rX//S9w18HBksQ6AvtYkw+EDAxusZN8xy4e/TvkCi4GCg9gPezHDz/qPDHxne8OxpW9Nh3bFNmzZ60WFSAHF6Mhk6qmEpBBdESHbCbAbKF2Ikgik5E5UpU0Y++ugjXRbxLhH52WefacA2Tc+ePXUJasCAAQ8tQWFd0pQlqDx58mgZzvTp0+vHUQVqdH0ykf2axusZr2u8d6DELF7bFDwM1H7AFWXdunV1PbJ8+fKuer1I2Prxxx+116ypcAWM0TRuaGGH80ciDuqWmwJZpej57cRksgoVKngEZkwRYn3P5JwAQE1vXLB5t7TEGh4unP78808xjROXoNzZb8EmZqfb0BISgdl+TdtT3054TYcDBmo/nTlzRiIiIuTQoUP6OV7EeHPAm4eJJk+erMEZV8U4VwRnbFXw7uXrhCYGJkuXLp2eMxq34A0NN+8lEhNhtIcpevvC0/2iCRelJm5hceoSFGYBMLPy22+/6edY60XmN9aDTYPXcsaMGeWDDz7QJTInvJbDCQN1PIOtWdiqgABdrFgxcQqsVaNrDy40MC349ddfa1LLV199pdOIyGQ3Cf6s9u3bp6MQzLxgXQ9r7hiJYCofU7ImwmsDa+oYjdpZydi+gsxwXCShe5JpnLgE1bdvX+2wh3N0n40bP368BsNPP/1UTLJnzx59HeP1vH79etdr2UkXoU7GQB1DuHKPLkwVmgb/3RhNOyXg2ZDw1rx5c73AwLkeOHBAp2fxxoZlBtxMhed8x44deq5z5swxOpkM08SYzrx8+bJuFYLdu3dL5syZ5aeffjJyD35kS1C4sFu2bJmRS1AYneLCAhdG7ubNm6fBG61yTYbAjdkA01/P4YL7qGMIU2lYS3rU9Q2+x8QXL5KC7ICHRJA7d+7o8evXr8uQIUOMDXjI6sU6JJLGsLXMhuQhfM00eG4x+sANF0ZY2y1SpIi+CWMkYipctOFiFG/AeDPGdjgk8iGgeBc/MQWeT0xzo1iI3XMY07MmL0GhYpavDPqSJUvKP//8I6bB+x3Wp91f06iohsGIya/ncMERdSymYKPLxHVfjJIwtYaAh+QsvBljZIo/wtq1a+s6sIlQzhKjaBRscT9vzAog6xQFZkySOHFifa7tvdMYpboXuKDAwv8/LjAuXLigIzx33klmJsAFGy58MP3trlu3brqmjrwXkyBhDFvfsFxmT3ljpsJJRWacjCPqGHIPvkOHDtUpQdSJdYe9yCgm8vHHH4tpMPJA0PCGIIK1SFNlyZJFiy0gULvDlb13hnKoYSYFMxd4I3NiRiySm7D9xlfQw9qqaZYvX64Xnpiu9x53mDqzZSeTof50uXLl9HNsfcN0PX4X7HaweQfzUBXwwes5su2RFFwM1AHIoPb27LPPSuPGjY0M1E4KeO6QfNWlSxe9CMKbL7LtsQ6JEUifPn3EJCgMgipqmIZ1WqCeMmWKvPvuu1ojGa8V9y1D+NjEQI3RKcpE4txw4ewE2BKJGgGA7YeA5xw3fM1mypYt5ADYWP0tBELWtysMJE2aVPs5ezt69Kh+zUTolV2oUCHtlZwqVSpr/fr11uzZs7Vt3bhx4yxTPXjwwBo0aJC2p0OLQNzQxrB3796WiUqWLGmtWrXKcppcuXJpK0AnwesY7SIpeNDGd8CAAdp7Gm04cUPvcrS8dG/xS8HBQO0H9Bf+6quvHjo+a9YsK0+ePJaJnBbwvN25c8fav3+/9vz+888/LVMtW7bMKl68uPX9999rH9zr16973EwOerjQdJJWrVpZU6dODfVphLUePXroxfyECROsPXv26C0iIkKP9erVK9SnF/aYTOYH9GTFbcSIEdr3FlavXq0lGFFnGKUNTXX37l2dAkeCCJKxUJGKAse9vrT79CX+3ExeN0Up2dKlSxtVoS46ZS0x9Y0tT8is985O79y5c8jOLVw4vfqb03GN2g/du3fXBBa8UBH47CpJWJs2OUgDChYgQFNwIBnLifLnz69r/igS4pSgh73HSMrC3x62Dnmvq5t4zk6DEr0FCxZ86DiOmVa+NxxxRB0AGJUicQh7TlEG0LR2kUTR5cRmEUh6QzDu0aOHMZ2ywo0Tq7+FEwZqoiDBdjdswbGLcGA3ALbycT914OuqI1jky5cv1KcStpzcgCgcMFATBQHaGdasWVNnWdA6EhBMUMwC07T21hwTYM/uwIEDJUWKFB77d32NqNHz2TQo4IP1aXR4ouDA/m4U8fHVgAiV1BDAKXgYqImCACMMrPdiXzLe4ABvaOiMhOljNOkwBZqELF68WKtM4eOoAvV///tfMQ2mvWfNmqVVs1DS0ntd3YSCIU6H2gBo1uLdvQ45OjhmanJkuGCgJgoCjKRRltU7AQdlUFHjGZnKFBhOvLhwmsjazKKkMpJSb968GbJziw+Y9U0UBCi1iOlC70CNNT3UKqfAcWqGvRPYSyF2VTrU3LdhFI2yp2hURMHFQE0UBI0aNdI9ySNHjpQKFSrosY0bN+qWPu/WhkSmwqyQe391bOu04WMsN6CMLwUXp76JAgTdmwoXLqzThNhXj6CMIhF220KsnaKO9rBhw7iFjxwFrU7Hjh3LphwhwkBNFISEGzQ4QZY31qrtpgvYPuQ+dUhEFB2c+iYKEGRNHz9+XAP1iRMntEUkAjMqfBERxRYDNVGAvP7661KlShXJmjWrJt8guxujbF9MrPBFRGZioCYKkC+++EJee+01bXaCvb3ooc0MbyLyF9eoiYKUfIO6yAzUROQvBmoiIiKDsdUMERGRwRioiYiIDMZATUREZDAGaiIiIoMxUBMRERmMgZqIiMhgDNREREQGY6AmIiISc/0/OI2lmqys7RMAAAAASUVORK5CYII=", "text/plain": ["<Figure size 500x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x = torch.arange(len(vocab))\n", "bar_width = 0.15\n", "\n", "fig, ax = plt.subplots(figsize=(5, 3))\n", "for i, T in enumerate(temperature):\n", "    rects = ax.bar(x + i * bar_width, scaled_probas[i], bar_width, label=f'Temperature = {T}')\n", "\n", "ax.set_ylabel('Probability')\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(vocab.keys(), rotation=90)\n", "ax.legend()\n", "\n", "plt.tight_layout()\n", "plt.savefig(\"temperature.pdf\")\n", "plt.show()"]}, {"cell_type": "markdown", "id": "a870d1ab", "metadata": {}, "source": ["### 5.3.2 Tok-k sampling"]}, {"cell_type": "code", "execution_count": 255, "id": "59332bf0", "metadata": {}, "outputs": [], "source": ["next_token_logits = torch.tensor(\n", "    [4.51, 0.89, -1.90, 6.75, 1.63, -1.62, -1.89, 6.28, 1.79]\n", ")"]}, {"cell_type": "code", "execution_count": 256, "id": "ec4c4178", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([6.7500, 6.2800, 4.5100]) tensor([3, 7, 0])\n"]}], "source": ["top_k = 3 \n", "top_logits, top_pos = torch.topk(next_token_logits, top_k)\n", "print(top_logits, top_pos)"]}, {"cell_type": "code", "execution_count": 257, "id": "33913131", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([4.5100,   -inf,   -inf, 6.7500,   -inf,   -inf,   -inf, 6.2800,   -inf])"]}, "execution_count": 257, "metadata": {}, "output_type": "execute_result"}], "source": ["new_logits = torch.where(\n", "    condition=next_token_logits < top_logits[-1],\n", "    input=torch.tensor(float(\"-inf\")),\n", "    other=next_token_logits\n", ")\n", "\n", "new_logits"]}, {"cell_type": "code", "execution_count": 258, "id": "e08b60c8", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0.0615, 0.0000, 0.0000, 0.5775, 0.0000, 0.0000, 0.0000, 0.3610, 0.0000])"]}, "execution_count": 258, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.softmax(new_logits, dim=-1)"]}, {"cell_type": "markdown", "id": "bff8dccf", "metadata": {}, "source": ["### 5.3.3 Modifying the text generation function"]}, {"cell_type": "code", "execution_count": 259, "id": "ecdf2926", "metadata": {}, "outputs": [], "source": ["def generate(model, idx, max_new_tokens, context_size, temperature=0.0, top_k=None, eos_id=None):\n", "\n", "    for _ in range(max_new_tokens):\n", "        idx_cond = idx[:, -context_size:]\n", "        with torch.no_grad():\n", "            logits = model(idx_cond)\n", "        logits = logits[:, -1, :]\n", "\n", "        if top_k is not None:\n", "            top_logits, _ = torch.topk(logits, top_k)\n", "            min_val = top_logits[:, -1]\n", "            logits = torch.where(logits < min_val, torch.tensor(float(\"-inf\")).to(logits.device), logits)\n", "\n", "        if temperature > 0.0:\n", "            logits = logits / temperature\n", "\n", "            probs = torch.softmax(logits, dim=-1)\n", "\n", "            idx_next = torch.multinomial(probs, num_samples=1)  \n", "\n", "        else:\n", "            idx_next = torch.argmax(logits, dim=-1, keepdim=True)  \n", "\n", "        if idx_next == eos_id:  \n", "            break\n", "\n", "        idx = torch.cat((idx, idx_next), dim=1) \n", "\n", "    return idx"]}, {"cell_type": "code", "execution_count": 260, "id": "3b5948da", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output text:\n", " Every effort moves you in of it's-, me that the of.I was his my\n"]}], "source": ["torch.manual_seed(123)\n", "\n", "token_ids = generate(\n", "    model=model,\n", "    idx=text_to_token_ids(\"Every effort moves you\", tokenizer),\n", "    max_new_tokens=15,\n", "    context_size=GPT_CONFIG_124M[\"context_length\"],\n", "    top_k=25,\n", "    temperature=1.2\n", ")\n", "\n", "print(\"Output text:\\n\", token_ids_to_text(token_ids, tokenizer))"]}, {"cell_type": "markdown", "id": "01888f12", "metadata": {}, "source": ["### 5.4 Loading and saving model weights in PyTorch"]}, {"cell_type": "code", "execution_count": 261, "id": "2836f97a", "metadata": {}, "outputs": [], "source": ["torch.save(model.state_dict(), \"FeatureimanbugAI.pth\")"]}, {"cell_type": "markdown", "id": "8043f3cb", "metadata": {}, "source": ["### 5.5 Loading pre-trained weights from OpenAI"]}, {"cell_type": "code", "execution_count": 262, "id": "034e632f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tenseflow verision: 2.19.0\n", "tqdm version: 4.67.1\n"]}], "source": ["print(\"Tenseflow verision:\", version(\"tensorflow\"))\n", "print(\"tqdm version:\", version(\"tqdm\"))\n"]}, {"cell_type": "code", "execution_count": 263, "id": "fa143115", "metadata": {}, "outputs": [], "source": ["from gpt_download import download_and_load_gpt2"]}, {"cell_type": "code", "execution_count": 264, "id": "dd1676c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File already exists and is up-to-date: gpt2/355M/checkpoint\n", "File already exists and is up-to-date: gpt2/355M/encoder.json\n", "File already exists and is up-to-date: gpt2/355M/hparams.json\n", "File already exists and is up-to-date: gpt2/355M/model.ckpt.data-00000-of-00001\n", "File already exists and is up-to-date: gpt2/355M/model.ckpt.index\n", "File already exists and is up-to-date: gpt2/355M/model.ckpt.meta\n", "File already exists and is up-to-date: gpt2/355M/vocab.bpe\n"]}], "source": ["settings, params = download_and_load_gpt2(model_size=\"355M\", models_dir=\"gpt2\")"]}, {"cell_type": "code", "execution_count": 265, "id": "78c69bce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Settings: {'n_vocab': 50257, 'n_ctx': 1024, 'n_embd': 1024, 'n_head': 16, 'n_layer': 24}\n"]}], "source": ["print(\"Settings:\", settings)"]}, {"cell_type": "code", "execution_count": 266, "id": "d3114c0d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameters: dict_keys(['blocks', 'b', 'g', 'wpe', 'wte'])\n"]}], "source": ["print(\"Parameters:\", params.keys())"]}, {"cell_type": "code", "execution_count": 267, "id": "ca464101", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[-0.0115168   0.00311915 -0.00729894 ... -0.05262156 -0.17569277\n", "   0.02565791]\n", " [-0.00861426  0.06360211 -0.01822355 ... -0.01364703 -0.12153847\n", "   0.05352487]\n", " [ 0.05854857  0.06891199  0.02622696 ... -0.10057542 -0.19788682\n", "  -0.0039184 ]\n", " ...\n", " [ 0.00162342 -0.04411932 -0.0517492  ... -0.10079621 -0.00865952\n", "   0.02637872]\n", " [-0.14374605 -0.04632217 -0.00650705 ...  0.07464293 -0.04721651\n", "  -0.03829013]\n", " [ 0.02065966 -0.01334631 -0.02586888 ...  0.03886637 -0.00233481\n", "   0.00107106]]\n", "Token embedding weights tensor shape: (50257, 1024)\n"]}], "source": ["print(params[\"wte\"])\n", "print(\"Token embedding weights tensor shape:\", params[\"wte\"].shape)"]}, {"cell_type": "code", "execution_count": 288, "id": "8430e9a7", "metadata": {}, "outputs": [], "source": ["model_configs = {\n", "    \"gpt2-small (124M)\": {\"emb_dim\": 768, \"n_layers\": 12, \"n_heads\": 12},\n", "    \"gpt2-medium (355M)\": {\"emb_dim\": 1024, \"n_layers\": 24, \"n_heads\": 16},\n", "    \"gpt2-large (774M)\": {\"emb_dim\": 1280, \"n_layers\": 36, \"n_heads\": 20},\n", "    \"gpt2-xl (1558M)\": {\"emb_dim\": 1600, \"n_layers\": 48, \"n_heads\": 25},\n", "}\n", "\n", "\n", "model_name = \"gpt2-medium (355M)\"  \n", "NEW_CONFIG = GPT_CONFIG_124M.copy()\n", "NEW_CONFIG.update(model_configs[model_name])\n", "NEW_CONFIG.update({\"context_length\": 1024, \"qkv_bias\": True})\n", "\n", "gpt = GPTModel(NEW_CONFIG)\n", "gpt.eval();"]}, {"cell_type": "code", "execution_count": 289, "id": "3342d3ba", "metadata": {}, "outputs": [], "source": ["def assign(left, right):\n", "    if left.shape != right.shape:\n", "        raise ValueError(f\"Shape mismatch. Left: {left.shape}, Right: {right.shape}\")\n", "    return torch.nn.Parameter(torch.tensor(right))"]}, {"cell_type": "code", "execution_count": 290, "id": "b4af6ce4", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "def load_weights_into_gpt(gpt, params):\n", "    gpt.pos_emb.weight = assign(gpt.pos_emb.weight, params[\"wpe\"])\n", "    gpt.tok_emb.weight = assign(gpt.tok_emb.weight, params[\"wte\"])\n", "    \n", "    for b in range(len(params[\"blocks\"])):\n", "        q_w, k_w, v_w = np.split(\n", "            (params[\"blocks\"][b][\"attn\"][\"c_attn\"])[\"w\"], 3, axis=-1)\n", "        gpt.trf_blocks[b].att.W_query.weight = assign(\n", "            gpt.trf_blocks[b].att.W_query.weight, q_w.T)\n", "        gpt.trf_blocks[b].att.W_key.weight = assign(\n", "            gpt.trf_blocks[b].att.W_key.weight, k_w.T)\n", "        gpt.trf_blocks[b].att.W_value.weight = assign(\n", "            gpt.trf_blocks[b].att.W_value.weight, v_w.T)\n", "\n", "        q_b, k_b, v_b = np.split(\n", "            (params[\"blocks\"][b][\"attn\"][\"c_attn\"])[\"b\"], 3, axis=-1)\n", "        gpt.trf_blocks[b].att.W_query.bias = assign(\n", "            gpt.trf_blocks[b].att.W_query.bias, q_b)\n", "        gpt.trf_blocks[b].att.W_key.bias = assign(\n", "            gpt.trf_blocks[b].att.W_key.bias, k_b)\n", "        gpt.trf_blocks[b].att.W_value.bias = assign(\n", "            gpt.trf_blocks[b].att.W_value.bias, v_b)\n", "        \n", "        gpt.trf_blocks[b].att.out_proj.weight = assign(\n", "            gpt.trf_blocks[b].att.out_proj.weight,\n", "            params[\"blocks\"][b][\"attn\"][\"c_proj\"][\"w\"].T)\n", "        gpt.trf_blocks[b].att.out_proj.bias = assign(\n", "            gpt.trf_blocks[b].att.out_proj.bias,\n", "            params[\"blocks\"][b][\"attn\"][\"c_proj\"][\"b\"])\n", "        \n", "        gpt.trf_blocks[b].ff.layers[0].weight = assign(\n", "            gpt.trf_blocks[b].ff.layers[0].weight,\n", "            params[\"blocks\"][b][\"mlp\"][\"c_fc\"][\"w\"].T)\n", "        gpt.trf_blocks[b].ff.layers[0].bias = assign(\n", "            gpt.trf_blocks[b].ff.layers[0].bias,\n", "            params[\"blocks\"][b][\"mlp\"][\"c_fc\"][\"b\"])\n", "        gpt.trf_blocks[b].ff.layers[2].weight = assign(\n", "            gpt.trf_blocks[b].ff.layers[2].weight,\n", "            params[\"blocks\"][b][\"mlp\"][\"c_proj\"][\"w\"].T)\n", "        gpt.trf_blocks[b].ff.layers[2].bias = assign(\n", "            gpt.trf_blocks[b].ff.layers[2].bias,\n", "            params[\"blocks\"][b][\"mlp\"][\"c_proj\"][\"b\"])\n", "        \n", "\n", "        if hasattr(gpt.trf_blocks[b].norm1, 'weight'):\n", "            gpt.trf_blocks[b].norm1.weight = assign(\n", "                gpt.trf_blocks[b].norm1.weight,\n", "                params[\"blocks\"][b][\"ln_1\"][\"g\"])\n", "            gpt.trf_blocks[b].norm1.bias = assign(\n", "                gpt.trf_blocks[b].norm1.bias,\n", "                params[\"blocks\"][b][\"ln_1\"][\"b\"])\n", "        <PERSON><PERSON>(gpt.trf_blocks[b].norm1, 'scale'):\n", "            gpt.trf_blocks[b].norm1.scale = assign(\n", "                gpt.trf_blocks[b].norm1.scale,\n", "                params[\"blocks\"][b][\"ln_1\"][\"g\"])\n", "            gpt.trf_blocks[b].norm1.shift = assign(\n", "                gpt.trf_blocks[b].norm1.shift,\n", "                params[\"blocks\"][b][\"ln_1\"][\"b\"])\n", "        \n", "        if hasattr(gpt.trf_blocks[b].norm2, 'weight'):\n", "            gpt.trf_blocks[b].norm2.weight = assign(\n", "                gpt.trf_blocks[b].norm2.weight,\n", "                params[\"blocks\"][b][\"ln_2\"][\"g\"])\n", "            gpt.trf_blocks[b].norm2.bias = assign(\n", "                gpt.trf_blocks[b].norm2.bias,\n", "                params[\"blocks\"][b][\"ln_2\"][\"b\"])\n", "        <PERSON><PERSON>(gpt.trf_blocks[b].norm2, 'scale'):\n", "            gpt.trf_blocks[b].norm2.scale = assign(\n", "                gpt.trf_blocks[b].norm2.scale,\n", "                params[\"blocks\"][b][\"ln_2\"][\"g\"])\n", "            gpt.trf_blocks[b].norm2.shift = assign(\n", "                gpt.trf_blocks[b].norm2.shift,\n", "                params[\"blocks\"][b][\"ln_2\"][\"b\"])\n", "\n", "    if hasattr(gpt.final_norm, 'weight'):\n", "        gpt.final_norm.weight = assign(gpt.final_norm.weight, params[\"g\"])\n", "        gpt.final_norm.bias = assign(gpt.final_norm.bias, params[\"b\"])\n", "    <PERSON><PERSON>(gpt.final_norm, 'scale'):\n", "        gpt.final_norm.scale = assign(gpt.final_norm.scale, params[\"g\"])\n", "        gpt.final_norm.shift = assign(gpt.final_norm.shift, params[\"b\"])\n", "\n", "    gpt.out_head.weight = assign(gpt.out_head.weight, params[\"wte\"])\n", "\n", "def load_weights_into_gpt_simple(gpt, params):\n", "    gpt.pos_emb.weight = assign(gpt.pos_emb.weight, params[\"wpe\"])\n", "    gpt.tok_emb.weight = assign(gpt.tok_emb.weight, params[\"wte\"])\n", "    \n", "    for b in range(len(params[\"blocks\"])):\n", "        q_w, k_w, v_w = np.split(\n", "            (params[\"blocks\"][b][\"attn\"][\"c_attn\"])[\"w\"], 3, axis=-1)\n", "        gpt.trf_blocks[b].att.W_query.weight = assign(\n", "            gpt.trf_blocks[b].att.W_query.weight, q_w.T)\n", "        gpt.trf_blocks[b].att.W_key.weight = assign(\n", "            gpt.trf_blocks[b].att.W_key.weight, k_w.T)\n", "        gpt.trf_blocks[b].att.W_value.weight = assign(\n", "            gpt.trf_blocks[b].att.W_value.weight, v_w.T)\n", "\n", "        q_b, k_b, v_b = np.split(\n", "            (params[\"blocks\"][b][\"attn\"][\"c_attn\"])[\"b\"], 3, axis=-1)\n", "        gpt.trf_blocks[b].att.W_query.bias = assign(\n", "            gpt.trf_blocks[b].att.W_query.bias, q_b)\n", "        gpt.trf_blocks[b].att.W_key.bias = assign(\n", "            gpt.trf_blocks[b].att.W_key.bias, k_b)\n", "        gpt.trf_blocks[b].att.W_value.bias = assign(\n", "            gpt.trf_blocks[b].att.W_value.bias, v_b)\n", "\n", "        gpt.trf_blocks[b].att.out_proj.weight = assign(\n", "            gpt.trf_blocks[b].att.out_proj.weight,\n", "            params[\"blocks\"][b][\"attn\"][\"c_proj\"][\"w\"].T)\n", "        gpt.trf_blocks[b].att.out_proj.bias = assign(\n", "            gpt.trf_blocks[b].att.out_proj.bias,\n", "            params[\"blocks\"][b][\"attn\"][\"c_proj\"][\"b\"])\n", "\n", "        gpt.trf_blocks[b].ff.layers[0].weight = assign(\n", "            gpt.trf_blocks[b].ff.layers[0].weight,\n", "            params[\"blocks\"][b][\"mlp\"][\"c_fc\"][\"w\"].T)\n", "        gpt.trf_blocks[b].ff.layers[0].bias = assign(\n", "            gpt.trf_blocks[b].ff.layers[0].bias,\n", "            params[\"blocks\"][b][\"mlp\"][\"c_fc\"][\"b\"])\n", "        gpt.trf_blocks[b].ff.layers[2].weight = assign(\n", "            gpt.trf_blocks[b].ff.layers[2].weight,\n", "            params[\"blocks\"][b][\"mlp\"][\"c_proj\"][\"w\"].T)\n", "        gpt.trf_blocks[b].ff.layers[2].bias = assign(\n", "            gpt.trf_blocks[b].ff.layers[2].bias,\n", "            params[\"blocks\"][b][\"mlp\"][\"c_proj\"][\"b\"])\n", "\n", "        gpt.trf_blocks[b].norm1.scale = assign(\n", "            gpt.trf_blocks[b].norm1.scale,\n", "            params[\"blocks\"][b][\"ln_1\"][\"g\"])\n", "        gpt.trf_blocks[b].norm1.shift = assign(\n", "            gpt.trf_blocks[b].norm1.shift,\n", "            params[\"blocks\"][b][\"ln_1\"][\"b\"])\n", "        gpt.trf_blocks[b].norm2.scale = assign(\n", "            gpt.trf_blocks[b].norm2.scale,\n", "            params[\"blocks\"][b][\"ln_2\"][\"g\"])\n", "        gpt.trf_blocks[b].norm2.shift = assign(\n", "            gpt.trf_blocks[b].norm2.shift,\n", "            params[\"blocks\"][b][\"ln_2\"][\"b\"])\n", "\n", "    gpt.final_norm.scale = assign(gpt.final_norm.scale, params[\"g\"])\n", "    gpt.final_norm.shift = assign(gpt.final_norm.shift, params[\"b\"])\n", "    \n", "    gpt.out_head.weight = assign(gpt.out_head.weight, params[\"wte\"])\n", "\n", "load_weights_into_gpt(gpt, params)\n", "gpt.to(device);"]}, {"cell_type": "code", "execution_count": 291, "id": "dda01cca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output text:\n", " Every effort moves you forward, but you must move forward with a purpose. You must move forward with a purpose. You must move forward with a\n"]}], "source": ["torch.manual_seed(123)\n", "token_ids = generate(\n", "    model=gpt,\n", "    idx=text_to_token_ids(\"Every effort moves you\", tokenizer).to(device),\n", "    max_new_tokens=25,\n", "    context_size=NEW_CONFIG[\"context_length\"],\n", "    top_k=50,\n", "    temperature=0.3  # Much lower temperature\n", ")\n", "print(\"Output text:\\n\", token_ids_to_text(token_ids, tokenizer))"]}], "metadata": {"kernelspec": {"display_name": "AIbYME", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}