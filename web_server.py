#!/usr/bin/env python3
"""
Featureimanbug AI - Web Server

Launch the web-based chat interface for Featureimanbug AI.
"""
import sys
import os
import argparse
import threading
import time
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.web.app import create_app, socketio
from src.web.routes import set_model_manager, set_chat_interface
from src.web.events import set_chat_interface as set_events_chat_interface
from src.web.model_manager import ModelManager


def check_model_files():
    """Check if required model files exist."""
    gpt2_path = "gpt2/355M"
    converted_path = "models/gpt2_355m_converted.pt"
    
    if os.path.exists(converted_path):
        return converted_path, True
    elif os.path.exists(gpt2_path):
        return gpt2_path, False
    else:
        return None, False


def main():
    """Main entry point for the web server."""
    parser = argparse.ArgumentParser(description='Featureimanbug AI Web Interface')
    parser.add_argument('--host', default='127.0.0.1', help='Host to bind to (default: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=5000, help='Port to bind to (default: 5000)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--model-path', help='Path to model (auto-detected if not specified)')
    parser.add_argument('--no-auto-load', action='store_true', help='Don\'t automatically load model on startup')
    
    args = parser.parse_args()
    
    print("🚀 Featureimanbug AI Web Interface")
    print("=" * 50)
    
    # Check for model files
    model_path, is_converted = check_model_files()
    
    if args.model_path:
        model_path = args.model_path
        is_converted = model_path.endswith('.pt') or model_path.endswith('.pth')
    
    if not model_path and not args.no_auto_load:
        print("❌ No model found!")
        print("\nOptions:")
        print("1. Convert GPT-2 model: python scripts/convert_gpt2.py")
        print("2. Use --no-auto-load to start without a model")
        print("3. Specify model path with --model-path")
        return
    
    # Create Flask app
    app = create_app({
        'DEBUG': args.debug
    })
    
    # Initialize model manager
    model_manager = ModelManager()
    
    # Set global references
    set_model_manager(model_manager)
    
    # Load model if specified
    if model_path and not args.no_auto_load:
        print(f"📂 Model found: {model_path}")
        print(f"🔧 Model type: {'Converted PyTorch' if is_converted else 'TensorFlow (will convert)'}")
        
        # Start model loading in background
        print("🔄 Starting model loading...")
        model_manager.load_model_async(model_path, is_converted)
        
        # Wait a moment for loading to start
        time.sleep(1)
        
        # Set up chat interface once model is loaded
        def setup_chat_interface():
            while not model_manager.is_loaded() and model_manager.is_loading():
                time.sleep(1)
            
            if model_manager.is_loaded():
                chat_interface = model_manager.get_chat_interface()
                set_chat_interface(chat_interface)
                set_events_chat_interface(chat_interface)
                print("✅ Chat interface ready")
            else:
                error = model_manager.get_load_error()
                print(f"❌ Model loading failed: {error}")
        
        # Start setup in background thread
        setup_thread = threading.Thread(target=setup_chat_interface, daemon=True)
        setup_thread.start()
    
    else:
        print("⚠️  Starting without model (use --model-path to specify)")
    
    # Print startup information
    print(f"\n🌐 Starting web server...")
    print(f"   Host: {args.host}")
    print(f"   Port: {args.port}")
    print(f"   Debug: {args.debug}")
    print(f"   URL: http://{args.host}:{args.port}")
    
    if args.host == '127.0.0.1':
        print(f"   Local URL: http://localhost:{args.port}")
    
    print("\n📝 Usage:")
    print("   1. Open the URL in your web browser")
    print("   2. Start chatting with Featureimanbug AI!")
    print("   3. Use Ctrl+C to stop the server")
    
    print("\n" + "=" * 50)
    
    try:
        # Start the server
        socketio.run(
            app,
            host=args.host,
            port=args.port,
            debug=args.debug,
            allow_unsafe_werkzeug=True  # For development
        )
    except KeyboardInterrupt:
        print("\n\n⏹️  Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Cleanup
        if model_manager.is_loaded():
            print("🧹 Cleaning up model...")
            model_manager.unload_model()
        print("👋 Goodbye!")


if __name__ == "__main__":
    main()
