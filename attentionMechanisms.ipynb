{"cells": [{"cell_type": "markdown", "id": "1ecfaa71-be46-4b8b-90ee-79a5c803669b", "metadata": {}, "source": ["# CHAPTER 3: CODING ATTENTION MECHANISMS"]}, {"cell_type": "markdown", "id": "86b4288b", "metadata": {}, "source": ["## 3.3 Attending to different parts of the input with self-attention"]}, {"cell_type": "markdown", "id": "4a884f0b", "metadata": {}, "source": ["### 3.3.1 A Simple Self-attention mechanism without trainable weights"]}, {"cell_type": "code", "execution_count": null, "id": "3c01b323-8719-4d97-ab02-9385f3eae4f7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "id": "5757645f-9c27-48e9-b3fe-b109f30eb217", "metadata": {}, "outputs": [], "source": ["import torch \n", "\n", "inputs = torch.tensor(\n", "    [[0.43, 0.15, 0.89], # your (x^1)\n", "     [0.55, 0.87, 0.66], # journey (x^2)\n", "     [0.57, 0.85, 0.64], # start (x^3)\n", "     [0.22, 0.58, 0.33], # with (x^4)\n", "     [0.77, 0.25, 0.10], # one (x^5)\n", "     [0.05, 0.80, 0.55]] # step (x^6) \n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "20018c81-ee04-4746-8baf-b40bd6a90525", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0.5500, 0.8700, 0.6600])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["input_query = inputs[1]\n", "input_query"]}, {"cell_type": "code", "execution_count": 6, "id": "47531644-0b28-47f2-994e-fcad72ae7ece", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0.4300, 0.1500, 0.8900])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["input_1 = inputs[0]\n", "input_1"]}, {"cell_type": "code", "execution_count": 7, "id": "f6b2f303-7632-4555-a6a9-a4d44c37e9f6", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9544"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["0.55*0.43 + 0.87 * 0.15 + 0.66 * 0.89"]}, {"cell_type": "code", "execution_count": 8, "id": "a62f2cd0-d8c4-4c12-9091-d0a126fc9e75", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor(0.9544)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.dot(input_query, input_1)"]}, {"cell_type": "code", "execution_count": 9, "id": "769db1c6-9d9e-4093-9d31-3c8a91ce4089", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor(0.9544)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["i = 0\n", "\n", "res = torch.dot(inputs[i], input_query)\n", "res"]}, {"cell_type": "code", "execution_count": 13, "id": "daf391b1-30db-4980-8f5a-44772fe7b528", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0., 0., 0., 0., 0., 0.])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.empty(inputs.shape[0])"]}, {"cell_type": "code", "execution_count": 14, "id": "02287b35-bfbe-4548-913b-ae8b9c0d8bf6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([0.9544, 1.4950, 1.4754, 0.8434, 0.7070, 1.0865])\n"]}], "source": ["query = inputs[1]\n", "\n", "attn_scores_2 = torch.empty(inputs.shape[0])\n", "for i, x_i in enumerate(inputs):\n", "    attn_scores_2[i] = torch.dot(x_i, input_query)\n", "\n", "print(attn_scores_2)"]}, {"cell_type": "code", "execution_count": 22, "id": "9dd1e443-b97a-4892-98e5-ab3b2eebde0b", "metadata": {}, "outputs": [], "source": ["attn_weight_2 = torch.softmax(attn_scores_2, dim=0)"]}, {"cell_type": "code", "execution_count": 23, "id": "ebad9e05-f6ef-4c33-8a77-6d95ec1d56c6", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0., 0., 0.])"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.zeros(query.shape)"]}, {"cell_type": "code", "execution_count": 96, "id": "aa6c4f5c-1787-4d74-98cc-a6401797eb0a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([0.4221, 0.6506, 0.5761], grad_fn=<AddBackward0>)\n"]}], "source": ["query = inputs[1]\n", "\n", "context_vec_2 = torch.zeros(query.shape)\n", "for i,x_i in enumerate(inputs):\n", "    context_vec_2 += attn_weight_2[i]*x_i\n", "\n", "print(context_vec_2)\n"]}, {"cell_type": "markdown", "id": "80300267-8559-42a6-934d-f4ff8dff1233", "metadata": {}, "source": ["## 3.3.2 A Simple self-attention mechanism without trainable weights"]}, {"cell_type": "code", "execution_count": 97, "id": "1bb7bb83-e08f-49b5-8caa-513be50663e7", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.4421, 0.5931, 0.5790],\n", "        [0.4419, 0.6515, 0.5683],\n", "        [0.4431, 0.6496, 0.5671],\n", "        [0.4304, 0.6298, 0.5510],\n", "        [0.4671, 0.5910, 0.5266],\n", "        [0.4177, 0.6503, 0.5645]])"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["attn_scores = inputs @ inputs.T\n", "attn_weights =  torch.softmax(attn_scores, dim=1)\n", "all_context_vecs = attn_weights @ inputs \n", "all_context_vecs"]}, {"cell_type": "markdown", "id": "a5f6a0af-06bb-4724-b6d7-3f03b52ef18c", "metadata": {}, "source": ["# 3.4 Implementing self-attention with trainable weights"]}, {"cell_type": "markdown", "id": "0c8a3ef7-3d15-4fbe-a348-15ba222d9ca1", "metadata": {}, "source": ["### 3.4.1 Computing the attention weights step by step"]}, {"cell_type": "code", "execution_count": 98, "id": "a38db418-6250-4e9e-9691-6fadac80ecc4", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["x_2 = inputs[1]\n", "d_in = inputs.shape[1]\n", "d_out = 2"]}, {"cell_type": "code", "execution_count": 99, "id": "98272f4f-54a9-4d82-a506-85cfaa4803c4", "metadata": {}, "outputs": [], "source": ["torch.manual_seed(123)\n", "\n", "W_query = torch.nn.Parameter(torch.rand(d_in, d_out))\n", "W_key = torch.nn.Parameter(torch.rand(d_in, d_out))\n", "W_value = torch.nn.Parameter(torch.rand(d_in, d_out))"]}, {"cell_type": "code", "execution_count": 100, "id": "7714dfad-ba8e-4c1c-92aa-cb2227844c7a", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0.4306, 1.4551], grad_fn=<SqueezeBackward4>)"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["query_2 = x_2 @ W_query\n", "\n", "query_2"]}, {"cell_type": "code", "execution_count": 101, "id": "df0d31d1-b5de-4e42-82f0-39665f7c93a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([6, 2])"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["keys = inputs @ W_key\n", "value = inputs @ W_value\n", "\n", "keys.shape"]}, {"cell_type": "code", "execution_count": 102, "id": "ee8cfc83-0782-4558-94d9-2c0d841e7d58", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.3669, 0.7646],\n", "        [0.4433, 1.1419],\n", "        [0.4361, 1.1156],\n", "        [0.2408, 0.6706],\n", "        [0.1827, 0.3292],\n", "        [0.3275, 0.9642]], grad_fn=<MmBackward0>)"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["keys"]}, {"cell_type": "code", "execution_count": 103, "id": "ee4ac350-f4e0-4501-96d4-7c52521b51aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor(1.8524, grad_fn=<DotBackward0>)"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["keys_2 = keys[1]\n", "attn_score_22 = torch.dot(query_2, keys_2)\n", "attn_score_22 "]}, {"cell_type": "code", "execution_count": 104, "id": "e439f956-7028-4e13-9887-24cf4350654a", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([1.2705, 1.8524, 1.8111, 1.0795, 0.5577, 1.5440],\n", "       grad_fn=<SqueezeBackward4>)"]}, "execution_count": 104, "metadata": {}, "output_type": "execute_result"}], "source": ["attn_scores_2 = query_2 @ keys.T\n", "attn_scores_2"]}, {"cell_type": "code", "execution_count": 105, "id": "17fa5bf4-caa0-4a41-8658-dc6c1616a9ce", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor(1.0000, grad_fn=<SumBackward0>)"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}], "source": ["d_k = keys.shape[1]\n", "\n", "attn_weight_2 = torch.softmax(attn_scores_2 / d_k**0.5, dim=-1)\n", "attn_weight_2.sum()"]}, {"cell_type": "code", "execution_count": 106, "id": "7bf35f3e-8fa2-44cd-bef9-d01a0f95384d", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0.3061, 0.8210], grad_fn=<SqueezeBackward4>)"]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}], "source": ["context_vec_2 = attn_weight_2 @ value\n", "context_vec_2"]}, {"cell_type": "markdown", "id": "6e6236c3-b612-4463-baa8-ec7d478579d9", "metadata": {}, "source": ["### 3.4.2 Implementing a compact SelfAttention class"]}, {"cell_type": "code", "execution_count": 107, "id": "21c61e6d-516b-489a-a11f-27371e8f2ed5", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.2996, 0.8053],\n", "        [0.3061, 0.8210],\n", "        [0.3058, 0.8203],\n", "        [0.2948, 0.7939],\n", "        [0.2927, 0.7891],\n", "        [0.2990, 0.8040]], grad_fn=<MmBackward0>)"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch.nn as nn \n", "\n", "class SelfAttention_v1(nn.<PERSON><PERSON><PERSON>):\n", "\n", "    def __init__(self, d_in, d_out):\n", "        super().__init__()\n", "        self.W_query = torch.nn.Parameter(torch.rand(d_in, d_out))\n", "        self.W_key = torch.nn.Parameter(torch.rand(d_in, d_out))\n", "        self.W_value = torch.nn.Parameter(torch.rand(d_in, d_out))\n", "\n", "    def forward(self, x):\n", "        queries = inputs @ W_query\n", "        keys = inputs @ W_key\n", "        values = inputs @ W_value\n", "\n", "        attn_scores = queries @ keys.T\n", "        attn_weights = torch.softmax(attn_scores / d_k**0.5, dim=-1)\n", "        context_vec = attn_weights @ values\n", "        \n", "        return context_vec\n", "\n", "torch.manual_seed(123)\n", "sa_v1 = SelfAttention_v1(d_in, d_out)\n", "sa_v1(inputs)"]}, {"cell_type": "code", "execution_count": 108, "id": "4c72a567-6f12-439f-8e30-e578cc3bd1f4", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[-0.5337, -0.1051],\n", "        [-0.5323, -0.1080],\n", "        [-0.5323, -0.1079],\n", "        [-0.5297, -0.1076],\n", "        [-0.5311, -0.1066],\n", "        [-0.5299, -0.1081]], grad_fn=<MmBackward0>)"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch.nn as nn \n", "\n", "class SelfAttention_v2(nn.<PERSON><PERSON><PERSON>):\n", "\n", "    def __init__(self, d_in, d_out, qkv_bias=False):\n", "        super().__init__()\n", "        self.W_query = torch.nn.Linear(d_in, d_out, bias=qkv_bias)\n", "        self.W_key = torch.nn.Linear(d_in, d_out, bias=qkv_bias)\n", "        self.W_value = torch.nn.Linear(d_in, d_out, bias=qkv_bias)\n", "\n", "    def forward(self, x):\n", "        queries = self.W_query(inputs)\n", "        keys = self.W_key(inputs)\n", "        values = self.W_value(inputs)\n", "\n", "        attn_scores = queries @ keys.T\n", "        attn_weights = torch.softmax(attn_scores / d_k**0.5, dim=-1)\n", "        context_vec = attn_weights @ values\n", "        \n", "        return context_vec\n", "\n", "torch.manual_seed(123)\n", "sa_v2 = SelfAttention_v2(d_in, d_out)\n", "sa_v2(inputs)"]}, {"cell_type": "markdown", "id": "c3048675-d31e-45c0-982f-a3470446dac1", "metadata": {}, "source": ["# 3.5 Hiding future words with causal attention"]}, {"cell_type": "markdown", "id": "3efb2d1b", "metadata": {}, "source": ["### 3.5.1 Applying a causal attention mask "]}, {"cell_type": "code", "execution_count": 109, "id": "bd9ee643", "metadata": {}, "outputs": [], "source": ["queries = sa_v2.W_query(inputs)\n", "keys = sa_v2.W_key(inputs)\n", "values = sa_v2.W_value(inputs)\n", "\n", "attn_scores = queries @ keys.T\n", "attn_weights = torch.softmax(attn_scores / d_k**0.5, dim=-1)"]}, {"cell_type": "code", "execution_count": 110, "id": "0d004c90", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.1717, 0.1762, 0.1761, 0.1555, 0.1627, 0.1579],\n", "        [0.1636, 0.1749, 0.1746, 0.1612, 0.1605, 0.1652],\n", "        [0.1637, 0.1749, 0.1746, 0.1611, 0.1606, 0.1651],\n", "        [0.1636, 0.1704, 0.1702, 0.1652, 0.1632, 0.1674],\n", "        [0.1667, 0.1722, 0.1721, 0.1618, 0.1633, 0.1639],\n", "        [0.1624, 0.1709, 0.1706, 0.1654, 0.1625, 0.1682]],\n", "       grad_fn=<SoftmaxBackward0>)"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["attn_weights"]}, {"cell_type": "code", "execution_count": 111, "id": "c40f3dab-7fbb-41f5-88d1-ba5018d0b3aa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[1., 0., 0., 0., 0., 0.],\n", "        [1., 1., 0., 0., 0., 0.],\n", "        [1., 1., 1., 0., 0., 0.],\n", "        [1., 1., 1., 1., 0., 0.],\n", "        [1., 1., 1., 1., 1., 0.],\n", "        [1., 1., 1., 1., 1., 1.]])\n"]}], "source": ["context_length = attn_scores.shape[0]\n", "mask_simple = torch.tril(torch.ones(context_length, context_length))\n", "print(mask_simple)"]}, {"cell_type": "code", "execution_count": 112, "id": "a9c94ad7-5b01-4e35-a10e-190b59dc0d18", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.1717, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000],\n", "        [0.1636, 0.1749, 0.0000, 0.0000, 0.0000, 0.0000],\n", "        [0.1637, 0.1749, 0.1746, 0.0000, 0.0000, 0.0000],\n", "        [0.1636, 0.1704, 0.1702, 0.1652, 0.0000, 0.0000],\n", "        [0.1667, 0.1722, 0.1721, 0.1618, 0.1633, 0.0000],\n", "        [0.1624, 0.1709, 0.1706, 0.1654, 0.1625, 0.1682]],\n", "       grad_fn=<MulBackward0>)"]}, "execution_count": 112, "metadata": {}, "output_type": "execute_result"}], "source": ["masked_simple = attn_weights * mask_simple\n", "masked_simple"]}, {"cell_type": "code", "execution_count": 113, "id": "278b063f-b29b-4362-a77e-8df350b4b036", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[1.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000],\n", "        [0.4833, 0.5167, 0.0000, 0.0000, 0.0000, 0.0000],\n", "        [0.3190, 0.3408, 0.3402, 0.0000, 0.0000, 0.0000],\n", "        [0.2445, 0.2545, 0.2542, 0.2468, 0.0000, 0.0000],\n", "        [0.1994, 0.2060, 0.2058, 0.1935, 0.1953, 0.0000],\n", "        [0.1624, 0.1709, 0.1706, 0.1654, 0.1625, 0.1682]],\n", "       grad_fn=<DivBackward0>)\n"]}], "source": ["row_sums = masked_simple.sum(dim=-1, keepdim=True)\n", "masked_simple_norm = masked_simple / row_sums\n", "print(masked_simple_norm)"]}, {"cell_type": "code", "execution_count": 114, "id": "3d518100-2586-49d7-b757-9acc93174412", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[0.3111,   -inf,   -inf,   -inf,   -inf,   -inf],\n", "        [0.1655, 0.2602,   -inf,   -inf,   -inf,   -inf],\n", "        [0.1667, 0.2602, 0.2577,   -inf,   -inf,   -inf],\n", "        [0.0510, 0.1080, 0.1064, 0.0643,   -inf,   -inf],\n", "        [0.1415, 0.1875, 0.1863, 0.0987, 0.1121,   -inf],\n", "        [0.0476, 0.1192, 0.1171, 0.0731, 0.0477, 0.0966]],\n", "       grad_fn=<MaskedFillBackward0>)\n"]}], "source": ["mask = torch.triu(torch.ones(context_length, context_length), diagonal=1)\n", "masked = attn_scores.masked_fill(mask.bool(), -torch.inf)\n", "print(masked)"]}, {"cell_type": "code", "execution_count": 115, "id": "5c506177-d02d-4702-8407-37babef88874", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000],\n", "        [0.4833, 0.5167, 0.0000, 0.0000, 0.0000, 0.0000],\n", "        [0.3190, 0.3408, 0.3402, 0.0000, 0.0000, 0.0000],\n", "        [0.2445, 0.2545, 0.2542, 0.2468, 0.0000, 0.0000],\n", "        [0.1994, 0.2060, 0.2058, 0.1935, 0.1953, 0.0000],\n", "        [0.1624, 0.1709, 0.1706, 0.1654, 0.1625, 0.1682]],\n", "       grad_fn=<SoftmaxBackward0>)"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["attn_weights = torch.softmax(masked / d_k**0.5, dim=-1)\n", "attn_weights"]}, {"cell_type": "markdown", "id": "4ee4aa27", "metadata": {}, "source": ["### 3.5.2 Masking additional attention weights with dropout"]}, {"cell_type": "code", "execution_count": 116, "id": "3bfb8f74-6e43-4a80-9cb8-85c86d811b79", "metadata": {}, "outputs": [], "source": ["torch.manual_seed(123)\n", "\n", "layer = torch.nn.Dropout(0.5) # 50% of the postion has been dropped\n"]}, {"cell_type": "code", "execution_count": 117, "id": "748795d7-8f54-4b00-bffe-04bdbf3de57b", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1., 1., 1., 1., 1., 1.],\n", "        [1., 1., 1., 1., 1., 1.],\n", "        [1., 1., 1., 1., 1., 1.],\n", "        [1., 1., 1., 1., 1., 1.],\n", "        [1., 1., 1., 1., 1., 1.],\n", "        [1., 1., 1., 1., 1., 1.]])"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": ["example = torch.ones(6, 6)\n", "example"]}, {"cell_type": "code", "execution_count": 118, "id": "4835e8a9-226f-4504-a21d-2acb0ea22ce7", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[2., 2., 0., 2., 2., 0.],\n", "        [0., 0., 0., 2., 0., 2.],\n", "        [2., 2., 2., 2., 0., 2.],\n", "        [0., 2., 2., 0., 0., 2.],\n", "        [0., 2., 0., 2., 0., 2.],\n", "        [0., 2., 2., 2., 2., 0.]])"]}, "execution_count": 118, "metadata": {}, "output_type": "execute_result"}], "source": ["layer(example)"]}, {"cell_type": "code", "execution_count": 119, "id": "e004b955-ed17-4614-98a6-70062f198491", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[2.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000],\n", "        [0.0000, 0.0000, 0.0000, 0.0000, 0.0000, 0.0000],\n", "        [0.0000, 0.6816, 0.6804, 0.0000, 0.0000, 0.0000],\n", "        [0.0000, 0.0000, 0.5085, 0.4936, 0.0000, 0.0000],\n", "        [0.0000, 0.0000, 0.0000, 0.0000, 0.3906, 0.0000],\n", "        [0.3249, 0.3418, 0.0000, 0.3308, 0.3249, 0.3363]],\n", "       grad_fn=<MulBackward0>)"]}, "execution_count": 119, "metadata": {}, "output_type": "execute_result"}], "source": ["layer(attn_weights)"]}, {"cell_type": "markdown", "id": "044977ed", "metadata": {}, "source": ["### 3.5.3 Implementing a compact causal self-attention class"]}, {"cell_type": "code", "execution_count": 120, "id": "17e70b86-f63f-4610-b405-9090edc4f097", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([2, 6, 3])"]}, "execution_count": 120, "metadata": {}, "output_type": "execute_result"}], "source": ["batch = torch.stack((inputs , inputs), dim=0)\n", "batch.shape"]}, {"cell_type": "code", "execution_count": 128, "id": "dd242f0f-1463-4551-9b8c-c65673bcf855", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[-0.4519,  0.2216],\n", "         [-0.5874,  0.0058],\n", "         [-0.6300, -0.0632],\n", "         [-0.5675, -0.0843],\n", "         [-0.5526, -0.0981],\n", "         [-0.5299, -0.1081]],\n", "\n", "        [[-0.4519,  0.2216],\n", "         [-0.5874,  0.0058],\n", "         [-0.6300, -0.0632],\n", "         [-0.5675, -0.0843],\n", "         [-0.5526, -0.0981],\n", "         [-0.5299, -0.1081]]], grad_fn=<UnsafeViewBackward0>)"]}, "execution_count": 128, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch.nn as nn \n", "\n", "class CausalAttention(nn.Module):\n", "\n", "    def __init__(self, d_in, d_out, context_length, dropout, qkv_bias=False):\n", "        super().__init__()\n", "        self.W_query = torch.nn.Linear(d_in, d_out, bias=qkv_bias)\n", "        self.W_key = torch.nn.Linear(d_in, d_out, bias=qkv_bias)\n", "        self.W_value = torch.nn.Linear(d_in, d_out, bias=qkv_bias)\n", "        self.dropout = torch.nn.Dropout(dropout)\n", "        self.register_buffer(\"mask\", torch.triu(torch.ones(context_length, context_length), diagonal=1))\n", "\n", "    def forward(self, x):\n", "        b, num_tokens, d_in = x.shape\n", "        queries = self.W_query(x)\n", "        keys = self.W_key(x)\n", "        values = self.W_value(x)\n", "\n", "        attn_scores = queries @ keys.transpose(1, 2)\n", "        attn_scores.masked_fill_(\n", "            self.mask.bool()[:num_tokens, :num_tokens], -torch.inf)\n", "        attn_weight = torch.softmax(\n", "            attn_scores / keys.shape[-1]**0.5, dim=-1\n", "        )\n", "        attn_weights = self.dropout(attn_weight)\n", "        \n", "        context_vec = attn_weights @ values\n", "        return context_vec\n", "\n", "torch.manual_seed(123)\n", "\n", "context_length = batch.shape[1]\n", "dropout = 0.0\n", "ca = CausalAttention(d_in, d_out, context_length, dropout)\n", "ca(batch)"]}, {"cell_type": "markdown", "id": "fc418140", "metadata": {}, "source": ["# 3.6 Extending single-head attention to multi-head attention"]}, {"cell_type": "markdown", "id": "bc5532bb", "metadata": {}, "source": ["### 3.6.1 Stacking Multiple single-head attention layers"]}, {"cell_type": "code", "execution_count": 132, "id": "de0b0725-6f26-4e19-8c2d-85070f260548", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[-0.4519,  0.2216,  0.4772,  0.1063],\n", "         [-0.5874,  0.0058,  0.5891,  0.3257],\n", "         [-0.6300, -0.0632,  0.6202,  0.3860],\n", "         [-0.5675, -0.0843,  0.5478,  0.3589],\n", "         [-0.5526, -0.0981,  0.5321,  0.3428],\n", "         [-0.5299, -0.1081,  0.5077,  0.3493]],\n", "\n", "        [[-0.4519,  0.2216,  0.4772,  0.1063],\n", "         [-0.5874,  0.0058,  0.5891,  0.3257],\n", "         [-0.6300, -0.0632,  0.6202,  0.3860],\n", "         [-0.5675, -0.0843,  0.5478,  0.3589],\n", "         [-0.5526, -0.0981,  0.5321,  0.3428],\n", "         [-0.5299, -0.1081,  0.5077,  0.3493]]], grad_fn=<CatBackward0>)"]}, "execution_count": 132, "metadata": {}, "output_type": "execute_result"}], "source": ["class MultiHeadAttentionWrapper(nn.Module):\n", "    def __init__(self, d_in, d_out, context_length, dropout, num_heads=2, qkv_bias=False):\n", "        super().__init__()\n", "        self.heads = nn.ModuleList([\n", "            CausalAttention(d_in, d_out, context_length, dropout, qkv_bias) for _ in range(num_heads)\n", "        ])\n", "\n", "    def forward(self, x):\n", "        return torch.cat([head(x) for head in self.heads], dim=-1)\n", "\n", "torch.manual_seed(123)\n", "\n", "context_length = batch.shape[1]\n", "d_in, d_out = 3 , 2\n", "\n", "mha = MultiHeadAttentionWrapper(d_in, d_out, context_length, dropout=0.0, num_heads=2)\n", "mha(batch)"]}, {"cell_type": "markdown", "id": "2bd36b56-09e0-44df-8ea4-4a12a593a8ec", "metadata": {}, "source": ["### 3.6.2 Implementing multi-head attention with weight splits"]}, {"cell_type": "code", "execution_count": 144, "id": "6babeb97-9319-4cf8-8d98-f9a431bd9275", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[[ 0.1184,  0.3120, -0.0847, -0.5774],\n", "         [ 0.0178,  0.3221, -0.0763, -0.4225],\n", "         [-0.0147,  0.3259, -0.0734, -0.3721],\n", "         [-0.0116,  0.3138, -0.0708, -0.3624],\n", "         [-0.0117,  0.2973, -0.0698, -0.3543],\n", "         [-0.0132,  0.2990, -0.0689, -0.3490]],\n", "\n", "        [[ 0.1184,  0.3120, -0.0847, -0.5774],\n", "         [ 0.0178,  0.3221, -0.0763, -0.4225],\n", "         [-0.0147,  0.3259, -0.0734, -0.3721],\n", "         [-0.0116,  0.3138, -0.0708, -0.3624],\n", "         [-0.0117,  0.2973, -0.0698, -0.3543],\n", "         [-0.0132,  0.2990, -0.0689, -0.3490]]], grad_fn=<ViewBackward0>)\n", "context_vecs.shape: torch.Size([2, 6, 4])\n"]}], "source": ["class MultiHeadAttention(nn.Module):\n", "    def __init__(self, d_in, d_out, context_length, dropout, num_heads, qkv_bias=False):\n", "        super().__init__()\n", "        assert (d_out % num_heads == 0), \\\n", "            \"d_out must be divisiable by num_heads\"\n", "\n", "        self.d_out = d_out \n", "        self.num_heads = num_heads\n", "        self.head_dim = d_out // num_heads\n", "\n", "        self.W_query = nn.Linear(d_in, d_out, bias=qkv_bias)\n", "        self.W_key = nn.Linear(d_in, d_out, bias=qkv_bias)\n", "        self.W_value = nn.Linear(d_in, d_out, bias=qkv_bias)\n", "        self.out_proj = nn.Linear(d_out, d_out)\n", "        self.dropout = nn.Dropout(dropout)\n", "        self.register_buffer(\n", "            \"mask\", \n", "            torch.triu(torch.ones(context_length, context_length)\n", "                       , diagonal=1)\n", "        )\n", "\n", "    def forward(self, x):\n", "        b, num_token, d_in = x.shape\n", "\n", "        keys = self.W_key(x)\n", "        queries = self.W_query(x)\n", "        values = self.W_value(x)\n", "\n", "        keys = keys.view(b, num_token, self.num_heads, self.head_dim)\n", "        values = values.view(b, num_token, self.num_heads, self.head_dim)\n", "        queries = queries.view(b, num_token, self.num_heads, self.head_dim)\n", "\n", "        keys = keys.transpose(1, 2)\n", "        values = values.transpose(1, 2)\n", "        queries = queries.transpose(1, 2)\n", "\n", "        attn_scores = queries @ keys.transpose(2, 3) \n", "\n", "        mask_bool = self.mask.bool()[:num_token, :num_token]\n", "\n", "        attn_scores.masked_fill_(mask_bool, -torch.inf)\n", "        attn_weights = torch.softmax(attn_scores / keys.shape[-1]**0.5, dim=-1)\n", "\n", "        context_vec = (attn_weights @ values).transpose(1,2)\n", "\n", "        context_vec = context_vec.contiguous().view(b, num_token, self.d_out)\n", "        context_vec = self.out_proj(context_vec) \n", "\n", "        return context_vec\n", "\n", "torch.manual_seed(123)\n", "\n", "batch_size, context_length, d_in = batch.shape\n", "d_out =4 \n", "mha = MultiHeadAttention(d_in, d_out, context_length, 0.0, num_heads=2)\n", "\n", "context_vecs = mha(batch)\n", "\n", "print(context_vecs)\n", "print(\"context_vecs.shape:\", context_vecs.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "cb2edf5e-4b47-448a-8898-6d0ab0403010", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}