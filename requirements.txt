# Core dependencies for Featureimanbug AI
torch >= 2.3.0             # Deep learning framework
tiktoken >= 0.5.1          # Tokenization
tqdm >= 4.66.1             # Progress bars
numpy >= 1.26, < 2.1       # Numerical computing

# Web interface dependencies
flask >= 2.3.0             # Web framework
flask-socketio >= 5.3.0    # Real-time communication
flask-cors >= 4.0.0        # Cross-origin requests

# Model loading dependencies
tensorflow >= 2.13.0       # For loading GPT-2 weights (CPU version is fine)

# Optional dependencies
matplotlib >= 3.7.1        # Plotting (optional)
jupyterlab >= 4.0          # Jupyter notebooks (optional)
pandas >= 2.2.1            # Data manipulation (optional)
psutil >= 5.9.5            # System monitoring (optional)
