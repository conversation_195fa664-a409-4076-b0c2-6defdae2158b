#!/usr/bin/env python3
"""
Featureimanbug AI - Convert GPT-2 model to PyTorch format.

This script converts the TensorFlow GPT-2 355M model to PyTorch format
for faster loading in the web interface.
"""
import sys
import os
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import torch
from src.model.gpt2_loader import load_gpt2_model, save_converted_model


def main():
    """Convert GPT-2 model to PyTorch format."""
    print("🔄 Featureimanbug AI - GPT-2 Model Converter")
    print("=" * 50)
    
    # Paths
    gpt2_path = "gpt2/355M"
    output_path = "models/gpt2_355m_converted.pt"
    
    # Check if GPT-2 model exists
    if not os.path.exists(gpt2_path):
        print(f"❌ GPT-2 model not found at {gpt2_path}")
        print("Please ensure the GPT-2 355M model is downloaded in the gpt2/355M directory")
        return
    
    # Check if already converted
    if os.path.exists(output_path):
        print(f"⚠️  Converted model already exists at {output_path}")
        response = input("Do you want to reconvert? (y/N): ").strip().lower()
        if response != 'y':
            print("✅ Using existing converted model")
            return
    
    try:
        # Load and convert model
        print(f"📂 Loading GPT-2 model from {gpt2_path}")
        model, tokenizer = load_gpt2_model(gpt2_path)
        
        # Test the model with a simple generation
        print("\n🧪 Testing model with sample generation...")
        test_prompt = "The future of artificial intelligence is"
        
        with torch.no_grad():
            # Encode prompt
            input_ids = torch.tensor(
                tokenizer.encode(test_prompt), 
                dtype=torch.long
            ).unsqueeze(0)
            
            # Generate
            generated = model.generate(
                input_ids, 
                max_new_tokens=30,
                temperature=0.8,
                top_p=0.9
            )
            
            # Decode
            generated_text = tokenizer.decode(generated[0].tolist())
            print(f"📝 Test generation: {generated_text}")
        
        # Save converted model
        print(f"\n💾 Saving converted model to {output_path}")
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Get config from model
        config = model.cfg
        save_converted_model(model, config, output_path)
        
        print("\n✅ Model conversion completed successfully!")
        print(f"📊 Model info:")
        print(f"   - Parameters: {model.get_num_params():,}")
        print(f"   - Layers: {config.n_layers}")
        print(f"   - Embedding dimension: {config.emb_dim}")
        print(f"   - Attention heads: {config.n_heads}")
        print(f"   - Context length: {config.context_length}")
        
        print(f"\n🚀 Ready to use with web interface!")
        print(f"   Converted model: {output_path}")
        
    except ImportError as e:
        if "tensorflow" in str(e).lower():
            print("❌ TensorFlow is required to convert GPT-2 weights")
            print("Install with: pip install tensorflow")
        else:
            print(f"❌ Import error: {e}")
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
