"""
Featureimanbug AI - Simple and robust GPT-2 model loader.

This module provides a simplified approach to loading GPT-2 weights
that works reliably across different TensorFlow versions.
"""
import torch
import torch.nn as nn
import json
import os
import numpy as np
from typing import Dict, Any, Optional
import tiktoken

from .gpt import GPTModel
from ..config import GPTConfig


def load_gpt2_config_simple(model_path: str) -> GPTConfig:
    """Load GPT-2 configuration from hparams.json."""
    hparams_path = os.path.join(model_path, "hparams.json")
    
    with open(hparams_path, 'r') as f:
        hparams = json.load(f)
    
    # Convert GPT-2 hparams to our config format
    config = GPTConfig(
        vocab_size=hparams["n_vocab"],
        context_length=hparams["n_ctx"],
        emb_dim=hparams["n_embd"],
        n_heads=hparams["n_head"],
        n_layers=hparams["n_layer"],
        drop_rate=0.1,
        qkv_bias=True,
        
        # Optimal generation settings for conversation
        max_new_tokens=100,
        temperature=0.8,
        top_k=50,
        top_p=0.9
    )
    
    return config


def load_gpt2_weights_simple(model_path: str) -> Dict[str, torch.Tensor]:
    """
    Load GPT-2 weights using a simple, robust approach.
    
    This function tries multiple methods to load the TensorFlow checkpoint
    and falls back gracefully if needed.
    """
    checkpoint_path = os.path.join(model_path, "model.ckpt")
    
    print(f"🔧 Loading weights from: {checkpoint_path}")
    
    # Method 1: Try using transformers library (most reliable)
    try:
        return _load_with_transformers(model_path)
    except Exception as e:
        print(f"⚠️  Transformers loading failed: {e}")
    
    # Method 2: Try direct TensorFlow loading
    try:
        return _load_with_tensorflow(checkpoint_path)
    except Exception as e:
        print(f"⚠️  TensorFlow loading failed: {e}")
    
    # Method 3: Try numpy-based loading
    try:
        return _load_with_numpy(checkpoint_path)
    except Exception as e:
        print(f"⚠️  Numpy loading failed: {e}")
    
    # If all methods fail, raise an error
    raise RuntimeError("All weight loading methods failed. The model will use random weights.")


def _load_with_transformers(model_path: str) -> Dict[str, torch.Tensor]:
    """Load weights using the transformers library (most reliable)."""
    try:
        from transformers import GPT2LMHeadModel
        
        print("📦 Using transformers library to load weights...")
        
        # Load the model using transformers
        model = GPT2LMHeadModel.from_pretrained(model_path, local_files_only=True)
        
        # Extract state dict
        state_dict = model.state_dict()
        
        # Convert to our format
        pytorch_weights = {}
        
        # Token embeddings
        pytorch_weights['tok_emb.weight'] = state_dict['transformer.wte.weight']
        
        # Position embeddings
        pytorch_weights['pos_emb.weight'] = state_dict['transformer.wpe.weight']
        
        # Transformer blocks
        for i in range(len([k for k in state_dict.keys() if k.startswith('transformer.h.')])):
            layer_prefix = f'transformer.h.{i}'
            pytorch_prefix = f'trf_blocks.{i}'
            
            # Attention
            c_attn_weight = state_dict[f'{layer_prefix}.attn.c_attn.weight']
            c_attn_bias = state_dict[f'{layer_prefix}.attn.c_attn.bias']
            
            # Split qkv
            emb_dim = c_attn_weight.shape[1]
            q_w, k_w, v_w = c_attn_weight.chunk(3, dim=0)
            q_b, k_b, v_b = c_attn_bias.chunk(3, dim=0)
            
            pytorch_weights[f'{pytorch_prefix}.att.W_query.weight'] = q_w.T
            pytorch_weights[f'{pytorch_prefix}.att.W_key.weight'] = k_w.T
            pytorch_weights[f'{pytorch_prefix}.att.W_value.weight'] = v_w.T
            pytorch_weights[f'{pytorch_prefix}.att.W_query.bias'] = q_b
            pytorch_weights[f'{pytorch_prefix}.att.W_key.bias'] = k_b
            pytorch_weights[f'{pytorch_prefix}.att.W_value.bias'] = v_b
            
            # Attention output
            pytorch_weights[f'{pytorch_prefix}.att.out_proj.weight'] = state_dict[f'{layer_prefix}.attn.c_proj.weight'].T
            pytorch_weights[f'{pytorch_prefix}.att.out_proj.bias'] = state_dict[f'{layer_prefix}.attn.c_proj.bias']
            
            # Layer norms
            pytorch_weights[f'{pytorch_prefix}.norm1.scale'] = state_dict[f'{layer_prefix}.ln_1.weight']
            pytorch_weights[f'{pytorch_prefix}.norm1.shift'] = state_dict[f'{layer_prefix}.ln_1.bias']
            pytorch_weights[f'{pytorch_prefix}.norm2.scale'] = state_dict[f'{layer_prefix}.ln_2.weight']
            pytorch_weights[f'{pytorch_prefix}.norm2.shift'] = state_dict[f'{layer_prefix}.ln_2.bias']
            
            # Feed-forward
            pytorch_weights[f'{pytorch_prefix}.ff.layers.0.weight'] = state_dict[f'{layer_prefix}.mlp.c_fc.weight'].T
            pytorch_weights[f'{pytorch_prefix}.ff.layers.0.bias'] = state_dict[f'{layer_prefix}.mlp.c_fc.bias']
            pytorch_weights[f'{pytorch_prefix}.ff.layers.2.weight'] = state_dict[f'{layer_prefix}.mlp.c_proj.weight'].T
            pytorch_weights[f'{pytorch_prefix}.ff.layers.2.bias'] = state_dict[f'{layer_prefix}.mlp.c_proj.bias']
        
        # Final layer norm
        pytorch_weights['final_norm.scale'] = state_dict['transformer.ln_f.weight']
        pytorch_weights['final_norm.shift'] = state_dict['transformer.ln_f.bias']
        
        # Output head
        pytorch_weights['out_head.weight'] = state_dict['lm_head.weight']
        
        print(f"✅ Loaded {len(pytorch_weights)} weight tensors using transformers")
        return pytorch_weights
        
    except ImportError:
        raise RuntimeError("transformers library not available")


def _load_with_tensorflow(checkpoint_path: str) -> Dict[str, torch.Tensor]:
    """Load weights using TensorFlow directly."""
    import tensorflow as tf
    
    print("🔧 Using TensorFlow to load weights...")
    
    # Try different TensorFlow loading methods
    try:
        # TensorFlow 2.x method
        checkpoint = tf.train.load_checkpoint(checkpoint_path)
        var_names = list(checkpoint.get_variable_to_shape_map().keys())
        
        tf_weights = {}
        for var_name in var_names:
            tensor = checkpoint.get_tensor(var_name)
            if hasattr(tensor, 'numpy'):
                tf_weights[var_name] = tensor.numpy()
            else:
                tf_weights[var_name] = np.array(tensor)
        
        return _convert_tf_weights_to_pytorch(tf_weights)
        
    except Exception as e:
        raise RuntimeError(f"TensorFlow loading failed: {e}")


def _load_with_numpy(checkpoint_path: str) -> Dict[str, torch.Tensor]:
    """Load weights using numpy arrays."""
    raise RuntimeError("Numpy loading not implemented yet")


def _convert_tf_weights_to_pytorch(tf_weights: Dict[str, np.ndarray]) -> Dict[str, torch.Tensor]:
    """Convert TensorFlow weights to PyTorch format."""
    pytorch_weights = {}
    
    # Token embeddings
    pytorch_weights['tok_emb.weight'] = torch.from_numpy(tf_weights['model/wte']).float()
    
    # Position embeddings
    pytorch_weights['pos_emb.weight'] = torch.from_numpy(tf_weights['model/wpe']).float()
    
    # Get number of layers
    n_layers = len([k for k in tf_weights.keys() if k.startswith('model/h')])
    
    for layer_idx in range(n_layers):
        layer_prefix = f'model/h{layer_idx}'
        pytorch_prefix = f'trf_blocks.{layer_idx}'
        
        # Attention weights
        c_attn_w = tf_weights[f'{layer_prefix}/attn/c_attn/w']
        c_attn_b = tf_weights[f'{layer_prefix}/attn/c_attn/b']
        
        # Split into q, k, v
        emb_dim = c_attn_w.shape[0]
        qkv_w = c_attn_w.reshape(emb_dim, 3, emb_dim)
        qkv_b = c_attn_b.reshape(3, emb_dim)
        
        pytorch_weights[f'{pytorch_prefix}.att.W_query.weight'] = torch.from_numpy(qkv_w[:, 0, :].T).float()
        pytorch_weights[f'{pytorch_prefix}.att.W_key.weight'] = torch.from_numpy(qkv_w[:, 1, :].T).float()
        pytorch_weights[f'{pytorch_prefix}.att.W_value.weight'] = torch.from_numpy(qkv_w[:, 2, :].T).float()
        
        pytorch_weights[f'{pytorch_prefix}.att.W_query.bias'] = torch.from_numpy(qkv_b[0]).float()
        pytorch_weights[f'{pytorch_prefix}.att.W_key.bias'] = torch.from_numpy(qkv_b[1]).float()
        pytorch_weights[f'{pytorch_prefix}.att.W_value.bias'] = torch.from_numpy(qkv_b[2]).float()
        
        # Attention output projection
        c_proj_w = tf_weights[f'{layer_prefix}/attn/c_proj/w']
        c_proj_b = tf_weights[f'{layer_prefix}/attn/c_proj/b']
        
        pytorch_weights[f'{pytorch_prefix}.att.out_proj.weight'] = torch.from_numpy(c_proj_w.T).float()
        pytorch_weights[f'{pytorch_prefix}.att.out_proj.bias'] = torch.from_numpy(c_proj_b).float()
        
        # Layer norms
        ln1_g = tf_weights[f'{layer_prefix}/ln_1/g']
        ln1_b = tf_weights[f'{layer_prefix}/ln_1/b']
        ln2_g = tf_weights[f'{layer_prefix}/ln_2/g']
        ln2_b = tf_weights[f'{layer_prefix}/ln_2/b']
        
        pytorch_weights[f'{pytorch_prefix}.norm1.scale'] = torch.from_numpy(ln1_g).float()
        pytorch_weights[f'{pytorch_prefix}.norm1.shift'] = torch.from_numpy(ln1_b).float()
        pytorch_weights[f'{pytorch_prefix}.norm2.scale'] = torch.from_numpy(ln2_g).float()
        pytorch_weights[f'{pytorch_prefix}.norm2.shift'] = torch.from_numpy(ln2_b).float()
        
        # Feed-forward network
        c_fc_w = tf_weights[f'{layer_prefix}/mlp/c_fc/w']
        c_fc_b = tf_weights[f'{layer_prefix}/mlp/c_fc/b']
        c_proj_mlp_w = tf_weights[f'{layer_prefix}/mlp/c_proj/w']
        c_proj_mlp_b = tf_weights[f'{layer_prefix}/mlp/c_proj/b']
        
        pytorch_weights[f'{pytorch_prefix}.ff.layers.0.weight'] = torch.from_numpy(c_fc_w.T).float()
        pytorch_weights[f'{pytorch_prefix}.ff.layers.0.bias'] = torch.from_numpy(c_fc_b).float()
        pytorch_weights[f'{pytorch_prefix}.ff.layers.2.weight'] = torch.from_numpy(c_proj_mlp_w.T).float()
        pytorch_weights[f'{pytorch_prefix}.ff.layers.2.bias'] = torch.from_numpy(c_proj_mlp_b).float()
    
    # Final layer norm
    ln_f_g = tf_weights['model/ln_f/g']
    ln_f_b = tf_weights['model/ln_f/b']
    
    pytorch_weights['final_norm.scale'] = torch.from_numpy(ln_f_g).float()
    pytorch_weights['final_norm.shift'] = torch.from_numpy(ln_f_b).float()
    
    # Output head (tied with token embeddings in GPT-2)
    pytorch_weights['out_head.weight'] = pytorch_weights['tok_emb.weight'].clone()
    
    return pytorch_weights


def load_gpt2_model_simple(model_path: str, device: Optional[torch.device] = None) -> tuple[GPTModel, tiktoken.Encoding]:
    """
    Load GPT-2 model with simplified, robust loading.
    
    Args:
        model_path: Path to GPT-2 model directory
        device: Device to load model on
        
    Returns:
        Tuple of (model, tokenizer)
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    print(f"🔧 Loading GPT-2 model from {model_path}")
    
    # Load configuration
    config = load_gpt2_config_simple(model_path)
    print(f"📊 Model config: {config.n_layers} layers, {config.emb_dim} dimensions, {config.n_heads} heads")
    
    # Create model
    model = GPTModel(config)
    
    # Try to load weights
    print("⚡ Loading pre-trained weights...")
    weights_loaded = False
    
    try:
        pytorch_weights = load_gpt2_weights_simple(model_path)
        
        # Load weights into model
        missing_keys, unexpected_keys = model.load_state_dict(pytorch_weights, strict=False)
        
        if len(missing_keys) == 0 and len(unexpected_keys) == 0:
            print("✅ All weights loaded perfectly!")
        else:
            print(f"⚠️  Partial loading: {len(missing_keys)} missing, {len(unexpected_keys)} unexpected")
        
        weights_loaded = True
        
    except Exception as e:
        print(f"❌ Failed to load pre-trained weights: {e}")
        print("🔄 Model will use randomly initialized weights")
        weights_loaded = False
    
    # Move to device
    model.to(device)
    model.eval()
    
    # Load tokenizer
    tokenizer = tiktoken.get_encoding("gpt2")
    
    print(f"🖥️  Model loaded on {device}")
    print(f"🔢 Model parameters: {model.get_num_params():,}")
    
    if weights_loaded:
        print("🎯 Model ready for coherent text generation!")
        
        # Test generation
        print("🧪 Testing generation...")
        test_prompt = "Hello, how are you?"
        
        with torch.no_grad():
            input_ids = torch.tensor(
                tokenizer.encode(test_prompt), 
                dtype=torch.long,
                device=device
            ).unsqueeze(0)
            
            generated = model.generate(
                input_ids, 
                max_new_tokens=10,
                temperature=0.8
            )
            
            generated_text = tokenizer.decode(generated[0].tolist())
            response = generated_text[len(test_prompt):].strip()
            print(f"🤖 Test response: '{response}'")
    else:
        print("⚠️  Model will generate random text (weights not loaded)")
    
    return model, tokenizer
