{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# 🤖 Featureimanbug AI - Language Model Training\n", "\n", "This notebook provides a complete training pipeline for the Featureimanbug AI language model, optimized for Google Colab.\n", "\n", "## Features:\n", "- 🚀 **Enhanced Transformer Architecture** with modern improvements\n", "- 🔧 **Colab-Optimized Training** with memory management\n", "- 📊 **Comprehensive Monitoring** with real-time visualizations\n", "- 💾 **Automatic Checkpointing** for session recovery\n", "- 🌐 **Multiple Data Sources** support\n", "- 📈 **Weights & Biases Integration** for experiment tracking\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 🔧 Setup and Installation\n", "\n", "First, let's set up the environment and install dependencies."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_deps"}, "outputs": [], "source": ["# Check GPU availability\n", "import torch\n", "print(f\"🖥️  CUDA available: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"📊 GPU: {torch.cuda.get_device_name()}\")\n", "    print(f\"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB\")\n", "else:\n", "    print(\"⚠️  No GPU detected. Training will be slow on CPU.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount_drive"}, "outputs": [], "source": ["# Mount Google Drive for persistent storage\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Create directories\n", "import os\n", "os.makedirs('/content/drive/MyDrive/featureimanbug_checkpoints', exist_ok=True)\n", "os.makedirs('/content/data', exist_ok=True)\n", "\n", "print(\"✅ Google Drive mounted and directories created\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "clone_repo"}, "outputs": [], "source": ["# Clone the Featureimanbug AI repository\n", "!git clone https://github.com/your-username/AIbYME.git /content/AIbYME\n", "# Or upload your code manually\n", "\n", "# Change to project directory\n", "%cd /content/AIbYME\n", "\n", "print(\"✅ Repository cloned\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_requirements"}, "outputs": [], "source": ["# Install requirements\n", "!pip install -q torch tiktoken tqdm numpy matplotlib seaborn datasets transformers wandb\n", "!pip install -q flask flask-socketio flask-cors  # For web interface compatibility\n", "\n", "print(\"✅ Dependencies installed\")"]}, {"cell_type": "markdown", "metadata": {"id": "config"}, "source": ["## ⚙️ Configuration\n", "\n", "Configure the model architecture and training parameters."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "imports"}, "outputs": [], "source": ["# Import necessary modules\n", "import sys\n", "sys.path.append('/content/AIbYME/src')\n", "sys.path.append('/content/AIbYME')\n", "\n", "import torch\n", "import tiktoken\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from IPython.display import display, clear_output\n", "import time\n", "\n", "# Featureimanbug AI modules\n", "from src.model.featureimanbug_model import FeatureimanbugModel, FeatureimanbugConfig\n", "from training.colab_trainer import ColabTrainer\n", "from training.data_utils import prepare_training_data, create_dataloader, optimize_batch_size_for_colab\n", "\n", "print(\"✅ Modules imported successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "model_config"}, "outputs": [], "source": ["# Model Configuration\n", "# Choose your model size based on available GPU memory\n", "\n", "# Small model (good for testing and limited GPU memory)\n", "config = FeatureimanbugConfig(\n", "    vocab_size=50257,\n", "    context_length=512,    # Reduced for Colab\n", "    emb_dim=512,          # Smaller than GPT-2\n", "    n_heads=8,\n", "    n_layers=8,\n", "    drop_rate=0.1,\n", "    \n", "    # Enhanced features\n", "    use_rotary_pos_emb=True,\n", "    use_glu_activation=True,\n", "    use_layer_scale=True,\n", "    use_pre_norm=True,\n", "    \n", "    # Training settings\n", "    learning_rate=3e-4,\n", "    batch_size=4,         # Will be optimized automatically\n", "    max_epochs=10,\n", "    warmup_steps=500,\n", "    weight_decay=0.01\n", ")\n", "\n", "# Optimize batch size for available memory\n", "optimal_batch_size = optimize_batch_size_for_colab(\n", "    max_length=config.context_length,\n", "    emb_dim=config.emb_dim,\n", "    n_layers=config.n_layers\n", ")\n", "config.batch_size = optimal_batch_size\n", "\n", "print(f\"📊 Model Configuration:\")\n", "print(f\"   Layers: {config.n_layers}\")\n", "print(f\"   Embedding dim: {config.emb_dim}\")\n", "print(f\"   Context length: {config.context_length}\")\n", "print(f\"   Batch size: {config.batch_size}\")\n", "print(f\"   Enhanced features: RoPE, GLU, LayerScale, PreNorm\")"]}, {"cell_type": "markdown", "metadata": {"id": "data"}, "source": ["## 📚 Data Preparation\n", "\n", "Load and prepare training data from various sources."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "data_source"}, "outputs": [], "source": ["# Choose your data source\n", "# Options:\n", "# 1. \"wikitext\" - WikiText dataset (good for general language modeling)\n", "# 2. \"openwebtext\" - OpenWebText subset (diverse web text)\n", "# 3. \"file:/path/to/your/file.txt\" - Custom text file\n", "# 4. Raw text string\n", "\n", "DATA_SOURCE = \"wikitext\"  # Change this to your preferred data source\n", "\n", "# For custom files, upload them to Colab and specify the path:\n", "# DATA_SOURCE = \"file:/content/your_text_file.txt\"\n", "\n", "print(f\"📖 Using data source: {DATA_SOURCE}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "prepare_data"}, "outputs": [], "source": ["# Initialize tokenizer\n", "tokenizer = tiktoken.get_encoding(\"gpt2\")\n", "print(f\"🔤 Tokenizer vocabulary size: {tokenizer.n_vocab}\")\n", "\n", "# Prepare datasets\n", "print(\"📚 Preparing training data...\")\n", "train_dataset, val_dataset = prepare_training_data(\n", "    data_source=DATA_SOURCE,\n", "    tokenizer=tokenizer,\n", "    max_length=config.context_length,\n", "    stride=config.context_length // 2,\n", "    train_split=0.9,\n", "    cache_dir=\"/content/data\"\n", ")\n", "\n", "print(f\"✅ Training dataset: {len(train_dataset)} sequences\")\n", "if val_dataset:\n", "    print(f\"✅ Validation dataset: {len(val_dataset)} sequences\")\n", "else:\n", "    print(\"ℹ️  No validation dataset (using all data for training)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_dataloaders"}, "outputs": [], "source": ["# Create data loaders\n", "train_dataloader = create_dataloader(\n", "    train_dataset,\n", "    batch_size=config.batch_size,\n", "    shuffle=True,\n", "    num_workers=2,  # Limited for Colab\n", "    pin_memory=True\n", ")\n", "\n", "val_dataloader = None\n", "if val_dataset:\n", "    val_dataloader = create_dataloader(\n", "        val_dataset,\n", "        batch_size=config.batch_size,\n", "        shuffle=False,\n", "        num_workers=2,\n", "        pin_memory=True\n", "    )\n", "\n", "print(f\"📦 Training batches: {len(train_dataloader)}\")\n", "if val_dataloader:\n", "    print(f\"📦 Validation batches: {len(val_dataloader)}\")\n", "\n", "# Estimate training time\n", "total_steps = len(train_dataloader) * config.max_epochs\n", "print(f\"🕐 Estimated total training steps: {total_steps:,}\")"]}, {"cell_type": "markdown", "metadata": {"id": "training"}, "source": ["## 🚀 Training\n", "\n", "Initialize the trainer and start training the model."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "wandb_setup"}, "outputs": [], "source": ["# Optional: Set up Weights & Biases for experiment tracking\n", "# Uncomment and run this cell if you want to use W&B\n", "\n", "# import wandb\n", "# wandb.login()  # You'll need to enter your W&B API key\n", "\n", "USE_WANDB = False  # Set to True if you want to use W&B\n", "print(f\"📊 Weights & Biases: {'Enabled' if USE_WANDB else 'Disabled'}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "initialize_trainer"}, "outputs": [], "source": ["# Initialize trainer\n", "trainer = <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    config=config,\n", "    model_name=\"featureimanbug-ai-v1\",\n", "    checkpoint_dir=\"/content/drive/MyDrive/featureimanbug_checkpoints\",\n", "    use_wandb=USE_WANDB,\n", "    wandb_project=\"featureimanbug-ai\"\n", ")\n", "\n", "print(\"✅ Trainer initialized\")\n", "print(f\"🧠 Model parameters: {trainer.model.get_num_params():,}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "start_training"}, "outputs": [], "source": ["# Start training\n", "print(\"🚀 Starting training...\")\n", "print(\"💡 Tip: Training will automatically save checkpoints to Google Drive\")\n", "print(\"💡 Tip: You can interrupt and resume training anytime\")\n", "print(\"\\n\" + \"=\"*60)\n", "\n", "try:\n", "    trainer.train(\n", "        train_dataloader=train_dataloader,\n", "        val_dataloader=val_dataloader,\n", "        resume_from_checkpoint=True  # Automatically resume if checkpoint exists\n", "    )\n", "    \n", "    print(\"\\n🎉 Training completed successfully!\")\n", "    \n", "except KeyboardInterrupt:\n", "    print(\"\\n⏹️  Training interrupted by user\")\n", "    print(\"💾 Checkpoint saved - you can resume training later\")\n", "    \n", "except Exception as e:\n", "    print(f\"\\n❌ Training failed: {e}\")\n", "    import traceback\n", "    traceback.print_exc()"]}, {"cell_type": "markdown", "metadata": {"id": "evaluation"}, "source": ["## 📊 Model Evaluation and Testing\n", "\n", "Test the trained model and generate sample text."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_best_model"}, "outputs": [], "source": ["# <PERSON>ad the best model\n", "best_model_path = \"/content/drive/MyDrive/featureimanbug_checkpoints/featureimanbug-ai-v1_best.pt\"\n", "\n", "if os.path.exists(best_model_path):\n", "    print(\"📂 Loading best model...\")\n", "    trainer.load_checkpoint(best_model_path)\n", "    print(\"✅ Best model loaded\")\n", "else:\n", "    print(\"ℹ️  Using current model (no best checkpoint found)\")\n", "\n", "# Set model to evaluation mode\n", "trainer.model.eval()\n", "print(\"🧪 Model ready for evaluation\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "test_generation"}, "outputs": [], "source": ["# Test text generation\n", "test_prompts = [\n", "    \"The future of artificial intelligence\",\n", "    \"Once upon a time\",\n", "    \"In the world of technology\",\n", "    \"Hello, how are you\",\n", "    \"The benefits of machine learning\"\n", "]\n", "\n", "print(\"🧪 Testing text generation...\")\n", "print(\"=\"*60)\n", "\n", "for i, prompt in enumerate(test_prompts, 1):\n", "    print(f\"\\n{i}. Prompt: '{prompt}'\")\n", "    print(\"-\" * 40)\n", "    \n", "    # Simple character-level generation for testing\n", "    # In a real implementation, you'd use proper tokenization\n", "    try:\n", "        generated_text = trainer.generate_sample(prompt, max_tokens=50)\n", "        print(f\"Generated: {generated_text}\")\n", "    except Exception as e:\n", "        print(f\"Generation failed: {e}\")\n", "\n", "print(\"\\n✅ Generation testing completed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "plot_final_results"}, "outputs": [], "source": ["# Plot final training results\n", "print(\"📈 Plotting training results...\")\n", "trainer.plot_training_progress()\n", "\n", "# Print training summary\n", "print(\"\\n📊 Training Summary:\")\n", "print(f\"   Total epochs: {trainer.current_epoch}\")\n", "print(f\"   Total steps: {trainer.current_step}\")\n", "print(f\"   Best loss: {trainer.best_loss:.4f}\")\n", "print(f\"   Final training loss: {trainer.training_losses[-1]:.4f}\" if trainer.training_losses else \"N/A\")\n", "if trainer.validation_losses:\n", "    print(f\"   Final validation loss: {trainer.validation_losses[-1]:.4f}\")"]}, {"cell_type": "markdown", "metadata": {"id": "export"}, "source": ["## 💾 Export Model for Web Interface\n", "\n", "Prepare the trained model for use with the Featureimanbug AI web interface."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "export_model"}, "outputs": [], "source": ["# Export model for web interface\n", "export_path = \"/content/drive/MyDrive/featureimanbug_ai_web_model.pt\"\n", "\n", "# Create export package\n", "export_package = {\n", "    'model_state_dict': trainer.model.state_dict(),\n", "    'config': config.__dict__,\n", "    'model_type': 'featureimanbug_ai',\n", "    'version': '1.0.0',\n", "    'training_info': {\n", "        'epochs': trainer.current_epoch,\n", "        'steps': trainer.current_step,\n", "        'best_loss': trainer.best_loss,\n", "        'final_loss': trainer.training_losses[-1] if trainer.training_losses else None\n", "    }\n", "}\n", "\n", "torch.save(export_package, export_path)\n", "print(f\"💾 Model exported for web interface: {export_path}\")\n", "\n", "# Also save to local Colab storage for download\n", "local_export_path = \"/content/featureimanbug_ai_model.pt\"\n", "torch.save(export_package, local_export_path)\n", "print(f\"💾 Model also saved locally: {local_export_path}\")\n", "\n", "print(\"\\n📋 To use this model with the web interface:\")\n", "print(\"1. Download the model file from Google Drive or Colab\")\n", "print(\"2. Place it in your AIbYME/models/ directory\")\n", "print(\"3. Update the web interface to load this model\")\n", "print(\"4. Start the web interface: python web_server.py\")"]}, {"cell_type": "markdown", "metadata": {"id": "conclusion"}, "source": ["## 🎉 Training Complete!\n", "\n", "Congratulations! You have successfully trained your own Featureimanbug AI language model.\n", "\n", "### What you've accomplished:\n", "- ✅ **Trained a custom transformer model** with modern architecture improvements\n", "- ✅ **Used advanced optimization techniques** for better performance\n", "- ✅ **Implemented comprehensive monitoring** and checkpointing\n", "- ✅ **Created a model compatible** with the Featureimanbug AI web interface\n", "\n", "### Next steps:\n", "1. **Download your trained model** from Google Drive\n", "2. **Integrate it with the web interface** for interactive chat\n", "3. **Fine-tune further** on specific datasets if needed\n", "4. **Experiment with different architectures** and hyperparameters\n", "\n", "### Tips for better results:\n", "- 🔄 **Train for more epochs** if you have time and resources\n", "- 📚 **Use larger, higher-quality datasets** for better performance\n", "- ⚙️ **Experiment with hyperparameters** like learning rate and model size\n", "- 🧪 **Try different architectures** by modifying the FeatureimanbugConfig\n", "\n", "Happy AI building! 🚀"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}