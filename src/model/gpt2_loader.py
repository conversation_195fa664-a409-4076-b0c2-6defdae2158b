"""
Featureimanbug AI - GPT-2 model loader and weight conversion.
"""
import torch
import torch.nn as nn
import json
import os
import numpy as np
from typing import Dict, Any, Optional
import tiktoken

from .gpt import GPTModel
from ..config import GPTConfig


def load_gpt2_config(model_path: str) -> GPTConfig:
    """
    Load GPT-2 configuration from hparams.json.
    
    Args:
        model_path: Path to GPT-2 model directory
        
    Returns:
        GPTConfig instance
    """
    hparams_path = os.path.join(model_path, "hparams.json")
    
    with open(hparams_path, 'r') as f:
        hparams = json.load(f)
    
    # Convert GPT-2 hparams to our config format
    config = GPTConfig(
        vocab_size=hparams["n_vocab"],
        context_length=hparams["n_ctx"],
        emb_dim=hparams["n_embd"],
        n_heads=hparams["n_head"],
        n_layers=hparams["n_layer"],
        drop_rate=0.1,  # Default dropout
        qkv_bias=True,  # GPT-2 uses bias
        
        # Generation settings
        max_new_tokens=200,
        temperature=0.8,
        top_k=50,
        top_p=0.9
    )
    
    return config


def load_gpt2_weights_from_tf(model_path: str) -> Dict[str, torch.Tensor]:
    """
    Load GPT-2 weights from TensorFlow checkpoint and convert to PyTorch format.
    
    Args:
        model_path: Path to GPT-2 model directory
        
    Returns:
        Dictionary of PyTorch tensors
    """
    try:
        import tensorflow as tf
    except ImportError:
        raise ImportError("TensorFlow is required to load GPT-2 weights. Install with: pip install tensorflow")
    
    checkpoint_path = os.path.join(model_path, "model.ckpt")
    
    # Load TensorFlow checkpoint
    tf_weights = {}
    reader = tf.train.NewCheckpointReader(checkpoint_path)
    var_to_shape_map = reader.get_variable_to_shape_map()
    
    for key in var_to_shape_map:
        tf_weights[key] = reader.get_tensor(key)
    
    # Convert to PyTorch format
    pytorch_weights = {}
    
    # Token embeddings
    pytorch_weights['tok_emb.weight'] = torch.from_numpy(tf_weights['model/wte']).float()
    
    # Position embeddings
    pytorch_weights['pos_emb.weight'] = torch.from_numpy(tf_weights['model/wpe']).float()
    
    # Transformer blocks
    n_layers = len([k for k in tf_weights.keys() if k.startswith('model/h')])
    
    for layer_idx in range(n_layers):
        layer_prefix = f'model/h{layer_idx}'
        pytorch_prefix = f'trf_blocks.{layer_idx}'
        
        # Attention weights (combined qkv)
        c_attn_w = tf_weights[f'{layer_prefix}/attn/c_attn/w']
        c_attn_b = tf_weights[f'{layer_prefix}/attn/c_attn/b']
        
        # Split into q, k, v
        emb_dim = c_attn_w.shape[0]
        qkv_w = c_attn_w.reshape(emb_dim, 3, emb_dim)
        qkv_b = c_attn_b.reshape(3, emb_dim)
        
        pytorch_weights[f'{pytorch_prefix}.att.W_query.weight'] = torch.from_numpy(qkv_w[:, 0, :].T).float()
        pytorch_weights[f'{pytorch_prefix}.att.W_key.weight'] = torch.from_numpy(qkv_w[:, 1, :].T).float()
        pytorch_weights[f'{pytorch_prefix}.att.W_value.weight'] = torch.from_numpy(qkv_w[:, 2, :].T).float()
        
        pytorch_weights[f'{pytorch_prefix}.att.W_query.bias'] = torch.from_numpy(qkv_b[0]).float()
        pytorch_weights[f'{pytorch_prefix}.att.W_key.bias'] = torch.from_numpy(qkv_b[1]).float()
        pytorch_weights[f'{pytorch_prefix}.att.W_value.bias'] = torch.from_numpy(qkv_b[2]).float()
        
        # Attention output projection
        c_proj_w = tf_weights[f'{layer_prefix}/attn/c_proj/w']
        c_proj_b = tf_weights[f'{layer_prefix}/attn/c_proj/b']
        
        pytorch_weights[f'{pytorch_prefix}.att.out_proj.weight'] = torch.from_numpy(c_proj_w.T).float()
        pytorch_weights[f'{pytorch_prefix}.att.out_proj.bias'] = torch.from_numpy(c_proj_b).float()
        
        # Layer norms
        ln1_g = tf_weights[f'{layer_prefix}/ln_1/g']
        ln1_b = tf_weights[f'{layer_prefix}/ln_1/b']
        ln2_g = tf_weights[f'{layer_prefix}/ln_2/g']
        ln2_b = tf_weights[f'{layer_prefix}/ln_2/b']
        
        pytorch_weights[f'{pytorch_prefix}.norm1.scale'] = torch.from_numpy(ln1_g).float()
        pytorch_weights[f'{pytorch_prefix}.norm1.shift'] = torch.from_numpy(ln1_b).float()
        pytorch_weights[f'{pytorch_prefix}.norm2.scale'] = torch.from_numpy(ln2_g).float()
        pytorch_weights[f'{pytorch_prefix}.norm2.shift'] = torch.from_numpy(ln2_b).float()
        
        # Feed-forward network
        c_fc_w = tf_weights[f'{layer_prefix}/mlp/c_fc/w']
        c_fc_b = tf_weights[f'{layer_prefix}/mlp/c_fc/b']
        c_proj_mlp_w = tf_weights[f'{layer_prefix}/mlp/c_proj/w']
        c_proj_mlp_b = tf_weights[f'{layer_prefix}/mlp/c_proj/b']
        
        pytorch_weights[f'{pytorch_prefix}.ff.layers.0.weight'] = torch.from_numpy(c_fc_w.T).float()
        pytorch_weights[f'{pytorch_prefix}.ff.layers.0.bias'] = torch.from_numpy(c_fc_b).float()
        pytorch_weights[f'{pytorch_prefix}.ff.layers.2.weight'] = torch.from_numpy(c_proj_mlp_w.T).float()
        pytorch_weights[f'{pytorch_prefix}.ff.layers.2.bias'] = torch.from_numpy(c_proj_mlp_b).float()
    
    # Final layer norm
    ln_f_g = tf_weights['model/ln_f/g']
    ln_f_b = tf_weights['model/ln_f/b']
    
    pytorch_weights['final_norm.scale'] = torch.from_numpy(ln_f_g).float()
    pytorch_weights['final_norm.shift'] = torch.from_numpy(ln_f_b).float()
    
    # Output head (tied with token embeddings in GPT-2)
    pytorch_weights['out_head.weight'] = pytorch_weights['tok_emb.weight'].clone()
    
    return pytorch_weights


def load_gpt2_model(model_path: str, device: Optional[torch.device] = None) -> tuple[GPTModel, tiktoken.Encoding]:
    """
    Load complete GPT-2 model with weights and tokenizer.
    
    Args:
        model_path: Path to GPT-2 model directory
        device: Device to load model on
        
    Returns:
        Tuple of (model, tokenizer)
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    print(f"🔧 Loading GPT-2 model from {model_path}")
    
    # Load configuration
    config = load_gpt2_config(model_path)
    print(f"📊 Model config: {config.n_layers} layers, {config.emb_dim} dimensions, {config.n_heads} heads")
    
    # Create model
    model = GPTModel(config)
    
    # Load weights
    print("⚡ Converting TensorFlow weights to PyTorch...")
    try:
        pytorch_weights = load_gpt2_weights_from_tf(model_path)
        
        # Load weights into model
        model.load_state_dict(pytorch_weights, strict=False)
        print("✅ Weights loaded successfully")
        
    except Exception as e:
        print(f"⚠️  Warning: Could not load TensorFlow weights: {e}")
        print("🔄 Using randomly initialized weights instead")
    
    # Move to device
    model.to(device)
    model.eval()
    
    # Load tokenizer
    tokenizer = tiktoken.get_encoding("gpt2")
    
    print(f"🖥️  Model loaded on {device}")
    print(f"🔢 Model parameters: {model.get_num_params():,}")
    
    return model, tokenizer


def save_converted_model(model: GPTModel, config: GPTConfig, save_path: str):
    """
    Save converted model in PyTorch format for faster loading.
    
    Args:
        model: GPT model
        config: Model configuration
        save_path: Path to save model
    """
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    checkpoint = {
        'model_state_dict': model.state_dict(),
        'config': config.to_dict(),
        'model_type': 'gpt2_355m',
        'featureimanbug_ai_version': '1.0.0'
    }
    
    torch.save(checkpoint, save_path)
    print(f"💾 Model saved to {save_path}")


def load_converted_model(checkpoint_path: str, device: Optional[torch.device] = None) -> tuple[GPTModel, GPTConfig, tiktoken.Encoding]:
    """
    Load previously converted PyTorch model.
    
    Args:
        checkpoint_path: Path to PyTorch checkpoint
        device: Device to load model on
        
    Returns:
        Tuple of (model, config, tokenizer)
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # Load config
    config = GPTConfig.from_dict(checkpoint['config'])
    
    # Create and load model
    model = GPTModel(config)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    model.eval()
    
    # Load tokenizer
    tokenizer = tiktoken.get_encoding("gpt2")
    
    print(f"✅ Converted model loaded from {checkpoint_path}")
    
    return model, config, tokenizer
