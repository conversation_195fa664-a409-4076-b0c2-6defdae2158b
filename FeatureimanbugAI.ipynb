{"cells": [{"cell_type": "code", "execution_count": 2, "id": "a2857eca", "metadata": {}, "outputs": [], "source": ["from previous_chapters import GPTModel\n", "import torch\n", "\n", "GPT_CONFIG_124M = {\n", "    \"vocab_size\": 50257,\n", "    \"context_length\": 256,\n", "    \"emb_dim\": 768,\n", "    \"n_heads\": 12,\n", "    \"n_layers\": 12,\n", "    \"drop_rate\": 0.1,\n", "    \"qkv_bias\": <PERSON><PERSON><PERSON>,\n", "}\n", "\n", "model = GPTModel(GPT_CONFIG_124M)"]}, {"cell_type": "code", "execution_count": 5, "id": "340f407b", "metadata": {}, "outputs": [{"data": {"text/plain": ["<All keys matched successfully>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "model.load_state_dict(torch.load(\"FeatureimanbugAI.pth\", map_location=device))"]}], "metadata": {"kernelspec": {"display_name": "AIbYME", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}